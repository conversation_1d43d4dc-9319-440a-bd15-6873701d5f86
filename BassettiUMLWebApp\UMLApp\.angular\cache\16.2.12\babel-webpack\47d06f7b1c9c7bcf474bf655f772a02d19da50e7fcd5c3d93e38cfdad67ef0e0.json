{"ast": null, "code": "import { BehaviorSubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\n/**\n * Error severity levels\n */\nexport var ErrorSeverity;\n(function (ErrorSeverity) {\n  ErrorSeverity[\"LOW\"] = \"low\";\n  ErrorSeverity[\"MEDIUM\"] = \"medium\";\n  ErrorSeverity[\"HIGH\"] = \"high\";\n  ErrorSeverity[\"CRITICAL\"] = \"critical\";\n})(ErrorSeverity || (ErrorSeverity = {}));\n/**\n * Error categories for better organization\n */\nexport var ErrorCategory;\n(function (ErrorCategory) {\n  ErrorCategory[\"NETWORK\"] = \"network\";\n  ErrorCategory[\"AUTHENTICATION\"] = \"authentication\";\n  ErrorCategory[\"AUTHORIZATION\"] = \"authorization\";\n  ErrorCategory[\"VALIDATION\"] = \"validation\";\n  ErrorCategory[\"BUSINESS_LOGIC\"] = \"business_logic\";\n  ErrorCategory[\"SYSTEM\"] = \"system\";\n  ErrorCategory[\"USER_INPUT\"] = \"user_input\";\n  ErrorCategory[\"EXTERNAL_SERVICE\"] = \"external_service\";\n})(ErrorCategory || (ErrorCategory = {}));\nexport class ErrorService {\n  constructor() {\n    this.destroy$ = new Subject();\n    this.errors$ = new Subject();\n    this.errorHistory$ = new BehaviorSubject([]);\n    this.statistics$ = new BehaviorSubject(this.initializeStatistics());\n    this.requestBlackList = [];\n    this.errorFilters = [];\n    this.errorHistory = [];\n    this.config = {\n      enableLogging: !environment.production,\n      enableAnalytics: true,\n      maxErrorHistory: 100,\n      debounceTime: 300,\n      defaultRetryAttempts: 3,\n      logLevel: environment.production ? 'error' : 'debug'\n    };\n    this.initializeErrorHandling();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.errors$.complete();\n    this.errorHistory$.complete();\n    this.statistics$.complete();\n  }\n  /**\n   * Initialize error handling pipeline\n   */\n  initializeErrorHandling() {\n    this.errors$.pipe(takeUntil(this.destroy$), debounceTime(this.config.debounceTime || 300), distinctUntilChanged((prev, curr) => prev.message === curr.message && prev.statusCode === curr.statusCode && Math.abs(curr.timestamp.getTime() - prev.timestamp.getTime()) < 5000)).subscribe(error => {\n      this.processError(error);\n    });\n  }\n  /**\n   * Add error with enhanced metadata and processing\n   */\n  addError(error, context) {\n    const enhancedError = this.createEnhancedError(error, context);\n    if (this.shouldSuppressError(enhancedError)) {\n      return enhancedError.id;\n    }\n    this.errors$.next(enhancedError);\n    return enhancedError.id;\n  }\n  /**\n   * Add custom error with specific configuration\n   */\n  addCustomError(message, category = ErrorCategory.SYSTEM, severity = ErrorSeverity.MEDIUM, context, options) {\n    const customError = {\n      isCustomError: true,\n      message,\n      error: {\n        errorKey: options?.errorKey,\n        message\n      },\n      ...options\n    };\n    return this.addError(customError, {\n      ...context,\n      component: context?.component || 'custom'\n    });\n  }\n  /**\n   * Get errors observable with optional filtering\n   */\n  getErrors(filter) {\n    return this.errors$.pipe(filter(error => this.matchesFilter(error, filter)), takeUntil(this.destroy$));\n  }\n  /**\n   * Get error history\n   */\n  getErrorHistory() {\n    return this.errorHistory$.asObservable();\n  }\n  /**\n   * Get error statistics\n   */\n  getStatistics() {\n    return this.statistics$.asObservable();\n  }\n  /**\n   * Register unhandled URL to the blacklist (backward compatibility)\n   */\n  registerUnhandledRequestURL(url) {\n    this.requestBlackList.push(url);\n  }\n  /**\n   * Check if request URL is blacklisted (backward compatibility)\n   */\n  isRequestBlackListed(url) {\n    return this.requestBlackList.some(rq => url.includes(rq));\n  }\n  /**\n   * Add error filter\n   */\n  addErrorFilter(filter) {\n    this.errorFilters.push(filter);\n  }\n  /**\n   * Remove error filter\n   */\n  removeErrorFilter(filter) {\n    const index = this.errorFilters.indexOf(filter);\n    if (index > -1) {\n      this.errorFilters.splice(index, 1);\n    }\n  }\n  /**\n   * Clear all error filters\n   */\n  clearErrorFilters() {\n    this.errorFilters = [];\n  }\n  /**\n   * Update service configuration\n   */\n  updateConfig(config) {\n    this.config = {\n      ...this.config,\n      ...config\n    };\n  }\n  /**\n   * Clear error history\n   */\n  clearErrorHistory() {\n    this.errorHistory = [];\n    this.errorHistory$.next([]);\n  }\n  /**\n   * Mark error as handled\n   */\n  markErrorAsHandled(errorId) {\n    const error = this.errorHistory.find(e => e.id === errorId);\n    if (error) {\n      error.handled = true;\n      this.errorHistory$.next([...this.errorHistory]);\n    }\n  }\n  /**\n   * Retry failed operation\n   */\n  retryError(errorId, retryFunction) {\n    const error = this.errorHistory.find(e => e.id === errorId);\n    if (!error || !error.retryable) {\n      throw new Error('Error is not retryable or not found');\n    }\n    error.retryCount = (error.retryCount || 0) + 1;\n    if (error.retryCount > (error.maxRetries || this.config.defaultRetryAttempts || 3)) {\n      throw new Error('Maximum retry attempts exceeded');\n    }\n    return retryFunction();\n  }\n  /**\n   * Initialize statistics object\n   */\n  initializeStatistics() {\n    return {\n      totalErrors: 0,\n      errorsByCategory: {},\n      errorsBySeverity: {},\n      errorsByStatusCode: {},\n      mostCommonErrors: [],\n      errorTrends: []\n    };\n  }\n  /**\n   * Create enhanced error from raw error\n   */\n  createEnhancedError(error, context) {\n    const timestamp = new Date();\n    const id = this.generateErrorId();\n    // Extract error information\n    const isHttpError = error?.status !== undefined;\n    const isCustomError = error?.isCustomError === true;\n    let message = '';\n    let statusCode;\n    let errorKey;\n    let category = ErrorCategory.SYSTEM;\n    let severity = ErrorSeverity.MEDIUM;\n    if (isCustomError) {\n      message = error.message || 'Custom error occurred';\n      errorKey = error.error?.errorKey;\n      category = this.determineCategory(error);\n      severity = this.determineSeverity(error);\n    } else if (isHttpError) {\n      statusCode = error.status;\n      message = error.error?.message || error.message || `HTTP ${statusCode} Error`;\n      errorKey = error.error?.errorKey;\n      category = this.categorizeHttpError(statusCode);\n      severity = this.determineSeverityFromStatus(statusCode);\n    } else {\n      message = error?.message || 'Unknown error occurred';\n      category = ErrorCategory.SYSTEM;\n      severity = ErrorSeverity.HIGH;\n    }\n    const enhancedError = {\n      id,\n      originalError: error,\n      message,\n      severity,\n      category,\n      context: {\n        timestamp,\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n        ...context\n      },\n      isCustomError,\n      errorKey,\n      statusCode,\n      retryable: this.isRetryable(error, statusCode),\n      retryCount: 0,\n      maxRetries: this.config.defaultRetryAttempts,\n      suppressDialog: error?.suppressDialog || false,\n      handled: false,\n      timestamp\n    };\n    return enhancedError;\n  }\n  /**\n   * Process error through the pipeline\n   */\n  processError(error) {\n    // Add to history\n    this.addToHistory(error);\n    // Update statistics\n    this.updateStatistics(error);\n    // Log error if enabled\n    if (this.config.enableLogging && !this.shouldSuppressLogging(error)) {\n      this.logError(error);\n    }\n    // Emit for dialog display (backward compatibility)\n    if (!error.suppressDialog) {\n      // Convert back to original format for backward compatibility\n      const legacyError = this.convertToLegacyFormat(error);\n      // Note: The dialog component will need to be updated to handle EnhancedError\n      // For now, we emit the legacy format\n    }\n  }\n  /**\n   * Check if error should be suppressed\n   */\n  shouldSuppressError(error) {\n    // Check URL blacklist\n    if (error.context.url && this.isRequestBlackListed(error.context.url)) {\n      return true;\n    }\n    // Check error filters\n    return this.errorFilters.some(filter => this.matchesFilter(error, filter));\n  }\n  /**\n   * Check if error matches filter\n   */\n  matchesFilter(error, filter) {\n    if (!filter) return true;\n    if (filter.statusCodes && error.statusCode && !filter.statusCodes.includes(error.statusCode)) {\n      return false;\n    }\n    if (filter.errorKeys && error.errorKey && !filter.errorKeys.includes(error.errorKey)) {\n      return false;\n    }\n    if (filter.categories && !filter.categories.includes(error.category)) {\n      return false;\n    }\n    if (filter.severities && !filter.severities.includes(error.severity)) {\n      return false;\n    }\n    if (filter.urlPatterns && error.context.url) {\n      const matchesUrl = filter.urlPatterns.some(pattern => error.context.url.includes(pattern));\n      if (!matchesUrl) return false;\n    }\n    return true;\n  }\n  /**\n   * Check if logging should be suppressed for this error\n   */\n  shouldSuppressLogging(error) {\n    return this.errorFilters.some(filter => filter.suppressLogging && this.matchesFilter(error, filter));\n  }\n  /**\n   * Add error to history with size management\n   */\n  addToHistory(error) {\n    this.errorHistory.unshift(error);\n    // Maintain max history size\n    if (this.errorHistory.length > (this.config.maxErrorHistory || 100)) {\n      this.errorHistory = this.errorHistory.slice(0, this.config.maxErrorHistory || 100);\n    }\n    this.errorHistory$.next([...this.errorHistory]);\n  }\n  /**\n   * Update error statistics\n   */\n  updateStatistics(error) {\n    const currentStats = this.statistics$.value;\n    // Update total count\n    currentStats.totalErrors++;\n    // Update category count\n    currentStats.errorsByCategory[error.category] = (currentStats.errorsByCategory[error.category] || 0) + 1;\n    // Update severity count\n    currentStats.errorsBySeverity[error.severity] = (currentStats.errorsBySeverity[error.severity] || 0) + 1;\n    // Update status code count\n    if (error.statusCode) {\n      currentStats.errorsByStatusCode[error.statusCode] = (currentStats.errorsByStatusCode[error.statusCode] || 0) + 1;\n    }\n    // Update most common errors\n    if (error.errorKey) {\n      const existingError = currentStats.mostCommonErrors.find(e => e.errorKey === error.errorKey);\n      if (existingError) {\n        existingError.count++;\n      } else {\n        currentStats.mostCommonErrors.push({\n          errorKey: error.errorKey,\n          count: 1\n        });\n      }\n      // Sort and keep top 10\n      currentStats.mostCommonErrors.sort((a, b) => b.count - a.count);\n      currentStats.mostCommonErrors = currentStats.mostCommonErrors.slice(0, 10);\n    }\n    // Update trends (hourly buckets)\n    const hourBucket = new Date(error.timestamp);\n    hourBucket.setMinutes(0, 0, 0);\n    const existingTrend = currentStats.errorTrends.find(t => t.timestamp.getTime() === hourBucket.getTime());\n    if (existingTrend) {\n      existingTrend.count++;\n    } else {\n      currentStats.errorTrends.push({\n        timestamp: hourBucket,\n        count: 1\n      });\n    }\n    // Keep only last 24 hours\n    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\n    currentStats.errorTrends = currentStats.errorTrends.filter(t => t.timestamp >= oneDayAgo);\n    this.statistics$.next(currentStats);\n  }\n  /**\n   * Log error based on configuration\n   */\n  logError(error) {\n    const logLevel = this.config.logLevel || 'error';\n    const logMessage = `[${error.severity.toUpperCase()}] ${error.category}: ${error.message}`;\n    const logData = {\n      id: error.id,\n      category: error.category,\n      severity: error.severity,\n      statusCode: error.statusCode,\n      errorKey: error.errorKey,\n      context: error.context,\n      originalError: error.originalError\n    };\n    switch (logLevel) {\n      case 'debug':\n        console.debug(logMessage, logData);\n        break;\n      case 'info':\n        if (error.severity === ErrorSeverity.LOW) {\n          console.info(logMessage, logData);\n        } else {\n          console.error(logMessage, logData);\n        }\n        break;\n      case 'warn':\n        if (error.severity === ErrorSeverity.LOW || error.severity === ErrorSeverity.MEDIUM) {\n          console.warn(logMessage, logData);\n        } else {\n          console.error(logMessage, logData);\n        }\n        break;\n      case 'error':\n      default:\n        console.error(logMessage, logData);\n        break;\n    }\n  }\n  /**\n   * Generate unique error ID\n   */\n  generateErrorId() {\n    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n  /**\n   * Determine error category from custom error\n   */\n  determineCategory(error) {\n    const errorKey = error.error?.errorKey?.toLowerCase() || '';\n    if (errorKey.includes('auth') || errorKey.includes('login') || errorKey.includes('token')) {\n      return ErrorCategory.AUTHENTICATION;\n    }\n    if (errorKey.includes('permission') || errorKey.includes('access') || errorKey.includes('forbidden')) {\n      return ErrorCategory.AUTHORIZATION;\n    }\n    if (errorKey.includes('validation') || errorKey.includes('invalid') || errorKey.includes('required')) {\n      return ErrorCategory.VALIDATION;\n    }\n    if (errorKey.includes('business') || errorKey.includes('rule') || errorKey.includes('logic')) {\n      return ErrorCategory.BUSINESS_LOGIC;\n    }\n    if (errorKey.includes('input') || errorKey.includes('user')) {\n      return ErrorCategory.USER_INPUT;\n    }\n    if (errorKey.includes('external') || errorKey.includes('service') || errorKey.includes('api')) {\n      return ErrorCategory.EXTERNAL_SERVICE;\n    }\n    return ErrorCategory.SYSTEM;\n  }\n  /**\n   * Determine error severity from custom error\n   */\n  determineSeverity(error) {\n    const errorKey = error.error?.errorKey?.toLowerCase() || '';\n    if (errorKey.includes('critical') || errorKey.includes('fatal')) {\n      return ErrorSeverity.CRITICAL;\n    }\n    if (errorKey.includes('high') || errorKey.includes('severe')) {\n      return ErrorSeverity.HIGH;\n    }\n    if (errorKey.includes('low') || errorKey.includes('minor')) {\n      return ErrorSeverity.LOW;\n    }\n    return ErrorSeverity.MEDIUM;\n  }\n  /**\n   * Categorize HTTP error by status code\n   */\n  categorizeHttpError(statusCode) {\n    if (statusCode === 401) {\n      return ErrorCategory.AUTHENTICATION;\n    }\n    if (statusCode === 403) {\n      return ErrorCategory.AUTHORIZATION;\n    }\n    if (statusCode >= 400 && statusCode < 500) {\n      return ErrorCategory.VALIDATION;\n    }\n    if (statusCode >= 500) {\n      return ErrorCategory.SYSTEM;\n    }\n    if (statusCode === 0 || statusCode < 0) {\n      return ErrorCategory.NETWORK;\n    }\n    return ErrorCategory.SYSTEM;\n  }\n  /**\n   * Determine severity from HTTP status code\n   */\n  determineSeverityFromStatus(statusCode) {\n    if (statusCode >= 500) {\n      return ErrorSeverity.HIGH;\n    }\n    if (statusCode === 401 || statusCode === 403) {\n      return ErrorSeverity.MEDIUM;\n    }\n    if (statusCode === 404) {\n      return ErrorSeverity.LOW;\n    }\n    if (statusCode >= 400 && statusCode < 500) {\n      return ErrorSeverity.MEDIUM;\n    }\n    if (statusCode === 0 || statusCode < 0) {\n      return ErrorSeverity.HIGH;\n    }\n    return ErrorSeverity.MEDIUM;\n  }\n  /**\n   * Check if error is retryable\n   */\n  isRetryable(error, statusCode) {\n    // Network errors are usually retryable\n    if (statusCode === 0 || statusCode === undefined) {\n      return true;\n    }\n    // Server errors are retryable\n    if (statusCode >= 500) {\n      return true;\n    }\n    // Timeout errors are retryable\n    if (statusCode === 408 || statusCode === 504) {\n      return true;\n    }\n    // Rate limiting is retryable\n    if (statusCode === 429) {\n      return true;\n    }\n    // Client errors are generally not retryable\n    if (statusCode >= 400 && statusCode < 500) {\n      return false;\n    }\n    return false;\n  }\n  /**\n   * Convert enhanced error back to legacy format for backward compatibility\n   */\n  convertToLegacyFormat(error) {\n    return {\n      ...error.originalError,\n      status: error.statusCode,\n      error: {\n        errorKey: error.errorKey,\n        message: error.message,\n        errorDetails: error.context.additionalData\n      },\n      isCustomError: error.isCustomError\n    };\n  }\n  static #_ = this.ɵfac = function ErrorService_Factory(t) {\n    return new (t || ErrorService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ErrorService,\n    factory: ErrorService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Subject", "debounceTime", "distinctUntilChanged", "takeUntil", "environment", "ErrorSeverity", "Error<PERSON>ate<PERSON><PERSON>", "ErrorService", "constructor", "destroy$", "errors$", "errorHistory$", "statistics$", "initializeStatistics", "requestBlackList", "errorFilters", "errorHistory", "config", "enableLogging", "production", "enableAnalytics", "maxError<PERSON><PERSON>ory", "defaultRetryAttempts", "logLevel", "initializeErrorHandling", "ngOnDestroy", "next", "complete", "pipe", "prev", "curr", "message", "statusCode", "Math", "abs", "timestamp", "getTime", "subscribe", "error", "processError", "addError", "context", "enhancedError", "createEnhancedError", "shouldSuppressError", "id", "addCustomError", "category", "SYSTEM", "severity", "MEDIUM", "options", "customError", "isCustomError", "<PERSON><PERSON><PERSON>", "component", "getErrors", "filter", "matchesFilter", "getErrorHistory", "asObservable", "getStatistics", "registerUnhandledRequestURL", "url", "push", "isRequestBlackListed", "some", "rq", "includes", "addErrorFilter", "removeErrorFilter", "index", "indexOf", "splice", "clearErrorFilters", "updateConfig", "clearErrorHistory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errorId", "find", "e", "handled", "retryError", "retryFunction", "retryable", "Error", "retryCount", "maxRetries", "totalErrors", "errorsByCategory", "errorsBySeverity", "errorsByStatusCode", "mostCommonErrors", "errorTrends", "Date", "generateErrorId", "isHttpError", "status", "undefined", "determineCategory", "determineSeverity", "categorizeHttpError", "determineSeverityFromStatus", "HIGH", "originalError", "window", "location", "href", "userAgent", "navigator", "isRetryable", "suppressDialog", "addToHistory", "updateStatistics", "shouldSuppressLogging", "logError", "legacyError", "convertToLegacyFormat", "statusCodes", "error<PERSON><PERSON><PERSON>", "categories", "severities", "urlPatterns", "matchesUrl", "pattern", "suppressLogging", "unshift", "length", "slice", "currentStats", "value", "existingError", "count", "sort", "a", "b", "hourBucket", "setMinutes", "existingTrend", "t", "oneDayAgo", "now", "logMessage", "toUpperCase", "logData", "console", "debug", "LOW", "info", "warn", "random", "toString", "substr", "toLowerCase", "AUTHENTICATION", "AUTHORIZATION", "VALIDATION", "BUSINESS_LOGIC", "USER_INPUT", "EXTERNAL_SERVICE", "CRITICAL", "NETWORK", "errorDetails", "additionalData", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\errors\\error.service.ts"], "sourcesContent": ["import { Injectable, OnDestroy } from '@angular/core';\r\nimport { BehaviorSubject, Observable, Subject } from 'rxjs';\r\nimport { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';\r\nimport { environment } from '../../../../environments/environment';\r\n\r\n/**\r\n * Error severity levels\r\n */\r\nexport enum ErrorSeverity {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical',\r\n}\r\n\r\n/**\r\n * Error categories for better organization\r\n */\r\nexport enum ErrorCategory {\r\n  NETWORK = 'network',\r\n  AUTHENTICATION = 'authentication',\r\n  AUTHORIZATION = 'authorization',\r\n  VALIDATION = 'validation',\r\n  BUSINESS_LOGIC = 'business_logic',\r\n  SYSTEM = 'system',\r\n  USER_INPUT = 'user_input',\r\n  EXTERNAL_SERVICE = 'external_service',\r\n}\r\n\r\n/**\r\n * Error context interface for additional metadata\r\n */\r\nexport interface ErrorContext {\r\n  userId?: string;\r\n  sessionId?: string;\r\n  userAgent?: string;\r\n  url?: string;\r\n  timestamp?: Date;\r\n  component?: string;\r\n  action?: string;\r\n  additionalData?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Enhanced error interface with comprehensive metadata\r\n */\r\nexport interface EnhancedError {\r\n  id: string;\r\n  originalError: any;\r\n  message: string;\r\n  severity: ErrorSeverity;\r\n  category: ErrorCategory;\r\n  context: ErrorContext;\r\n  isCustomError?: boolean;\r\n  errorKey?: string;\r\n  statusCode?: number;\r\n  retryable?: boolean;\r\n  retryCount?: number;\r\n  maxRetries?: number;\r\n  suppressDialog?: boolean;\r\n  handled?: boolean;\r\n  timestamp: Date;\r\n}\r\n\r\n/**\r\n * Error filter configuration\r\n */\r\nexport interface ErrorFilter {\r\n  urlPatterns?: string[];\r\n  statusCodes?: number[];\r\n  errorKeys?: string[];\r\n  categories?: ErrorCategory[];\r\n  severities?: ErrorSeverity[];\r\n  suppressDialog?: boolean;\r\n  suppressLogging?: boolean;\r\n}\r\n\r\n/**\r\n * Error service configuration\r\n */\r\nexport interface ErrorServiceConfig {\r\n  enableLogging?: boolean;\r\n  enableAnalytics?: boolean;\r\n  maxErrorHistory?: number;\r\n  debounceTime?: number;\r\n  defaultRetryAttempts?: number;\r\n  logLevel?: 'error' | 'warn' | 'info' | 'debug';\r\n}\r\n\r\n/**\r\n * Error statistics for analytics\r\n */\r\nexport interface ErrorStatistics {\r\n  totalErrors: number;\r\n  errorsByCategory: Record<ErrorCategory, number>;\r\n  errorsBySeverity: Record<ErrorSeverity, number>;\r\n  errorsByStatusCode: Record<number, number>;\r\n  mostCommonErrors: Array<{ errorKey: string; count: number }>;\r\n  errorTrends: Array<{ timestamp: Date; count: number }>;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ErrorService implements OnDestroy {\r\n  private readonly destroy$ = new Subject<void>();\r\n  private readonly errors$ = new Subject<EnhancedError>();\r\n  private readonly errorHistory$ = new BehaviorSubject<EnhancedError[]>([]);\r\n  private readonly statistics$ = new BehaviorSubject<ErrorStatistics>(\r\n    this.initializeStatistics()\r\n  );\r\n\r\n  private requestBlackList: string[] = [];\r\n  private errorFilters: ErrorFilter[] = [];\r\n  private errorHistory: EnhancedError[] = [];\r\n  private config: ErrorServiceConfig;\r\n\r\n  constructor() {\r\n    this.config = {\r\n      enableLogging: !environment.production,\r\n      enableAnalytics: true,\r\n      maxErrorHistory: 100,\r\n      debounceTime: 300,\r\n      defaultRetryAttempts: 3,\r\n      logLevel: environment.production ? 'error' : 'debug',\r\n    };\r\n\r\n    this.initializeErrorHandling();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.errors$.complete();\r\n    this.errorHistory$.complete();\r\n    this.statistics$.complete();\r\n  }\r\n\r\n  /**\r\n   * Initialize error handling pipeline\r\n   */\r\n  private initializeErrorHandling(): void {\r\n    this.errors$\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        debounceTime(this.config.debounceTime || 300),\r\n        distinctUntilChanged(\r\n          (prev, curr) =>\r\n            prev.message === curr.message &&\r\n            prev.statusCode === curr.statusCode &&\r\n            Math.abs(curr.timestamp.getTime() - prev.timestamp.getTime()) < 5000\r\n        )\r\n      )\r\n      .subscribe((error) => {\r\n        this.processError(error);\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Add error with enhanced metadata and processing\r\n   */\r\n  public addError(error: any, context?: Partial<ErrorContext>): string {\r\n    const enhancedError = this.createEnhancedError(error, context);\r\n\r\n    if (this.shouldSuppressError(enhancedError)) {\r\n      return enhancedError.id;\r\n    }\r\n\r\n    this.errors$.next(enhancedError);\r\n    return enhancedError.id;\r\n  }\r\n\r\n  /**\r\n   * Add custom error with specific configuration\r\n   */\r\n  public addCustomError(\r\n    message: string,\r\n    category: ErrorCategory = ErrorCategory.SYSTEM,\r\n    severity: ErrorSeverity = ErrorSeverity.MEDIUM,\r\n    context?: Partial<ErrorContext>,\r\n    options?: {\r\n      errorKey?: string;\r\n      retryable?: boolean;\r\n      suppressDialog?: boolean;\r\n    }\r\n  ): string {\r\n    const customError = {\r\n      isCustomError: true,\r\n      message,\r\n      error: {\r\n        errorKey: options?.errorKey,\r\n        message,\r\n      },\r\n      ...options,\r\n    };\r\n\r\n    return this.addError(customError, {\r\n      ...context,\r\n      component: context?.component || 'custom',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get errors observable with optional filtering\r\n   */\r\n  public getErrors(filter?: Partial<ErrorFilter>): Observable<EnhancedError> {\r\n    return this.errors$.pipe(\r\n      filter((error) => this.matchesFilter(error, filter)),\r\n      takeUntil(this.destroy$)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get error history\r\n   */\r\n  public getErrorHistory(): Observable<EnhancedError[]> {\r\n    return this.errorHistory$.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Get error statistics\r\n   */\r\n  public getStatistics(): Observable<ErrorStatistics> {\r\n    return this.statistics$.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Register unhandled URL to the blacklist (backward compatibility)\r\n   */\r\n  public registerUnhandledRequestURL(url: string): void {\r\n    this.requestBlackList.push(url);\r\n  }\r\n\r\n  /**\r\n   * Check if request URL is blacklisted (backward compatibility)\r\n   */\r\n  public isRequestBlackListed(url: string): boolean {\r\n    return this.requestBlackList.some((rq) => url.includes(rq));\r\n  }\r\n\r\n  /**\r\n   * Add error filter\r\n   */\r\n  public addErrorFilter(filter: ErrorFilter): void {\r\n    this.errorFilters.push(filter);\r\n  }\r\n\r\n  /**\r\n   * Remove error filter\r\n   */\r\n  public removeErrorFilter(filter: ErrorFilter): void {\r\n    const index = this.errorFilters.indexOf(filter);\r\n    if (index > -1) {\r\n      this.errorFilters.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear all error filters\r\n   */\r\n  public clearErrorFilters(): void {\r\n    this.errorFilters = [];\r\n  }\r\n\r\n  /**\r\n   * Update service configuration\r\n   */\r\n  public updateConfig(config: Partial<ErrorServiceConfig>): void {\r\n    this.config = { ...this.config, ...config };\r\n  }\r\n\r\n  /**\r\n   * Clear error history\r\n   */\r\n  public clearErrorHistory(): void {\r\n    this.errorHistory = [];\r\n    this.errorHistory$.next([]);\r\n  }\r\n\r\n  /**\r\n   * Mark error as handled\r\n   */\r\n  public markErrorAsHandled(errorId: string): void {\r\n    const error = this.errorHistory.find((e) => e.id === errorId);\r\n    if (error) {\r\n      error.handled = true;\r\n      this.errorHistory$.next([...this.errorHistory]);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retry failed operation\r\n   */\r\n  public retryError(\r\n    errorId: string,\r\n    retryFunction: () => Observable<any>\r\n  ): Observable<any> {\r\n    const error = this.errorHistory.find((e) => e.id === errorId);\r\n    if (!error || !error.retryable) {\r\n      throw new Error('Error is not retryable or not found');\r\n    }\r\n\r\n    error.retryCount = (error.retryCount || 0) + 1;\r\n\r\n    if (\r\n      error.retryCount >\r\n      (error.maxRetries || this.config.defaultRetryAttempts || 3)\r\n    ) {\r\n      throw new Error('Maximum retry attempts exceeded');\r\n    }\r\n\r\n    return retryFunction();\r\n  }\r\n\r\n  /**\r\n   * Initialize statistics object\r\n   */\r\n  private initializeStatistics(): ErrorStatistics {\r\n    return {\r\n      totalErrors: 0,\r\n      errorsByCategory: {} as Record<ErrorCategory, number>,\r\n      errorsBySeverity: {} as Record<ErrorSeverity, number>,\r\n      errorsByStatusCode: {},\r\n      mostCommonErrors: [],\r\n      errorTrends: [],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create enhanced error from raw error\r\n   */\r\n  private createEnhancedError(\r\n    error: any,\r\n    context?: Partial<ErrorContext>\r\n  ): EnhancedError {\r\n    const timestamp = new Date();\r\n    const id = this.generateErrorId();\r\n\r\n    // Extract error information\r\n    const isHttpError = error?.status !== undefined;\r\n    const isCustomError = error?.isCustomError === true;\r\n\r\n    let message = '';\r\n    let statusCode: number | undefined;\r\n    let errorKey: string | undefined;\r\n    let category = ErrorCategory.SYSTEM;\r\n    let severity = ErrorSeverity.MEDIUM;\r\n\r\n    if (isCustomError) {\r\n      message = error.message || 'Custom error occurred';\r\n      errorKey = error.error?.errorKey;\r\n      category = this.determineCategory(error);\r\n      severity = this.determineSeverity(error);\r\n    } else if (isHttpError) {\r\n      statusCode = error.status;\r\n      message =\r\n        error.error?.message || error.message || `HTTP ${statusCode} Error`;\r\n      errorKey = error.error?.errorKey;\r\n      category = this.categorizeHttpError(statusCode);\r\n      severity = this.determineSeverityFromStatus(statusCode);\r\n    } else {\r\n      message = error?.message || 'Unknown error occurred';\r\n      category = ErrorCategory.SYSTEM;\r\n      severity = ErrorSeverity.HIGH;\r\n    }\r\n\r\n    const enhancedError: EnhancedError = {\r\n      id,\r\n      originalError: error,\r\n      message,\r\n      severity,\r\n      category,\r\n      context: {\r\n        timestamp,\r\n        url: window.location.href,\r\n        userAgent: navigator.userAgent,\r\n        ...context,\r\n      },\r\n      isCustomError,\r\n      errorKey,\r\n      statusCode,\r\n      retryable: this.isRetryable(error, statusCode),\r\n      retryCount: 0,\r\n      maxRetries: this.config.defaultRetryAttempts,\r\n      suppressDialog: error?.suppressDialog || false,\r\n      handled: false,\r\n      timestamp,\r\n    };\r\n\r\n    return enhancedError;\r\n  }\r\n\r\n  /**\r\n   * Process error through the pipeline\r\n   */\r\n  private processError(error: EnhancedError): void {\r\n    // Add to history\r\n    this.addToHistory(error);\r\n\r\n    // Update statistics\r\n    this.updateStatistics(error);\r\n\r\n    // Log error if enabled\r\n    if (this.config.enableLogging && !this.shouldSuppressLogging(error)) {\r\n      this.logError(error);\r\n    }\r\n\r\n    // Emit for dialog display (backward compatibility)\r\n    if (!error.suppressDialog) {\r\n      // Convert back to original format for backward compatibility\r\n      const legacyError = this.convertToLegacyFormat(error);\r\n      // Note: The dialog component will need to be updated to handle EnhancedError\r\n      // For now, we emit the legacy format\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if error should be suppressed\r\n   */\r\n  private shouldSuppressError(error: EnhancedError): boolean {\r\n    // Check URL blacklist\r\n    if (error.context.url && this.isRequestBlackListed(error.context.url)) {\r\n      return true;\r\n    }\r\n\r\n    // Check error filters\r\n    return this.errorFilters.some((filter) =>\r\n      this.matchesFilter(error, filter)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if error matches filter\r\n   */\r\n  private matchesFilter(\r\n    error: EnhancedError,\r\n    filter?: Partial<ErrorFilter>\r\n  ): boolean {\r\n    if (!filter) return true;\r\n\r\n    if (\r\n      filter.statusCodes &&\r\n      error.statusCode &&\r\n      !filter.statusCodes.includes(error.statusCode)\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    if (\r\n      filter.errorKeys &&\r\n      error.errorKey &&\r\n      !filter.errorKeys.includes(error.errorKey)\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    if (filter.categories && !filter.categories.includes(error.category)) {\r\n      return false;\r\n    }\r\n\r\n    if (filter.severities && !filter.severities.includes(error.severity)) {\r\n      return false;\r\n    }\r\n\r\n    if (filter.urlPatterns && error.context.url) {\r\n      const matchesUrl = filter.urlPatterns.some((pattern) =>\r\n        error.context.url!.includes(pattern)\r\n      );\r\n      if (!matchesUrl) return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Check if logging should be suppressed for this error\r\n   */\r\n  private shouldSuppressLogging(error: EnhancedError): boolean {\r\n    return this.errorFilters.some(\r\n      (filter) => filter.suppressLogging && this.matchesFilter(error, filter)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Add error to history with size management\r\n   */\r\n  private addToHistory(error: EnhancedError): void {\r\n    this.errorHistory.unshift(error);\r\n\r\n    // Maintain max history size\r\n    if (this.errorHistory.length > (this.config.maxErrorHistory || 100)) {\r\n      this.errorHistory = this.errorHistory.slice(\r\n        0,\r\n        this.config.maxErrorHistory || 100\r\n      );\r\n    }\r\n\r\n    this.errorHistory$.next([...this.errorHistory]);\r\n  }\r\n\r\n  /**\r\n   * Update error statistics\r\n   */\r\n  private updateStatistics(error: EnhancedError): void {\r\n    const currentStats = this.statistics$.value;\r\n\r\n    // Update total count\r\n    currentStats.totalErrors++;\r\n\r\n    // Update category count\r\n    currentStats.errorsByCategory[error.category] =\r\n      (currentStats.errorsByCategory[error.category] || 0) + 1;\r\n\r\n    // Update severity count\r\n    currentStats.errorsBySeverity[error.severity] =\r\n      (currentStats.errorsBySeverity[error.severity] || 0) + 1;\r\n\r\n    // Update status code count\r\n    if (error.statusCode) {\r\n      currentStats.errorsByStatusCode[error.statusCode] =\r\n        (currentStats.errorsByStatusCode[error.statusCode] || 0) + 1;\r\n    }\r\n\r\n    // Update most common errors\r\n    if (error.errorKey) {\r\n      const existingError = currentStats.mostCommonErrors.find(\r\n        (e) => e.errorKey === error.errorKey\r\n      );\r\n      if (existingError) {\r\n        existingError.count++;\r\n      } else {\r\n        currentStats.mostCommonErrors.push({\r\n          errorKey: error.errorKey,\r\n          count: 1,\r\n        });\r\n      }\r\n\r\n      // Sort and keep top 10\r\n      currentStats.mostCommonErrors.sort((a, b) => b.count - a.count);\r\n      currentStats.mostCommonErrors = currentStats.mostCommonErrors.slice(\r\n        0,\r\n        10\r\n      );\r\n    }\r\n\r\n    // Update trends (hourly buckets)\r\n    const hourBucket = new Date(error.timestamp);\r\n    hourBucket.setMinutes(0, 0, 0);\r\n\r\n    const existingTrend = currentStats.errorTrends.find(\r\n      (t) => t.timestamp.getTime() === hourBucket.getTime()\r\n    );\r\n\r\n    if (existingTrend) {\r\n      existingTrend.count++;\r\n    } else {\r\n      currentStats.errorTrends.push({ timestamp: hourBucket, count: 1 });\r\n    }\r\n\r\n    // Keep only last 24 hours\r\n    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\r\n    currentStats.errorTrends = currentStats.errorTrends.filter(\r\n      (t) => t.timestamp >= oneDayAgo\r\n    );\r\n\r\n    this.statistics$.next(currentStats);\r\n  }\r\n\r\n  /**\r\n   * Log error based on configuration\r\n   */\r\n  private logError(error: EnhancedError): void {\r\n    const logLevel = this.config.logLevel || 'error';\r\n    const logMessage = `[${error.severity.toUpperCase()}] ${error.category}: ${\r\n      error.message\r\n    }`;\r\n    const logData = {\r\n      id: error.id,\r\n      category: error.category,\r\n      severity: error.severity,\r\n      statusCode: error.statusCode,\r\n      errorKey: error.errorKey,\r\n      context: error.context,\r\n      originalError: error.originalError,\r\n    };\r\n\r\n    switch (logLevel) {\r\n      case 'debug':\r\n        console.debug(logMessage, logData);\r\n        break;\r\n      case 'info':\r\n        if (error.severity === ErrorSeverity.LOW) {\r\n          console.info(logMessage, logData);\r\n        } else {\r\n          console.error(logMessage, logData);\r\n        }\r\n        break;\r\n      case 'warn':\r\n        if (\r\n          error.severity === ErrorSeverity.LOW ||\r\n          error.severity === ErrorSeverity.MEDIUM\r\n        ) {\r\n          console.warn(logMessage, logData);\r\n        } else {\r\n          console.error(logMessage, logData);\r\n        }\r\n        break;\r\n      case 'error':\r\n      default:\r\n        console.error(logMessage, logData);\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate unique error ID\r\n   */\r\n  private generateErrorId(): string {\r\n    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  /**\r\n   * Determine error category from custom error\r\n   */\r\n  private determineCategory(error: any): ErrorCategory {\r\n    const errorKey = error.error?.errorKey?.toLowerCase() || '';\r\n\r\n    if (\r\n      errorKey.includes('auth') ||\r\n      errorKey.includes('login') ||\r\n      errorKey.includes('token')\r\n    ) {\r\n      return ErrorCategory.AUTHENTICATION;\r\n    }\r\n    if (\r\n      errorKey.includes('permission') ||\r\n      errorKey.includes('access') ||\r\n      errorKey.includes('forbidden')\r\n    ) {\r\n      return ErrorCategory.AUTHORIZATION;\r\n    }\r\n    if (\r\n      errorKey.includes('validation') ||\r\n      errorKey.includes('invalid') ||\r\n      errorKey.includes('required')\r\n    ) {\r\n      return ErrorCategory.VALIDATION;\r\n    }\r\n    if (\r\n      errorKey.includes('business') ||\r\n      errorKey.includes('rule') ||\r\n      errorKey.includes('logic')\r\n    ) {\r\n      return ErrorCategory.BUSINESS_LOGIC;\r\n    }\r\n    if (errorKey.includes('input') || errorKey.includes('user')) {\r\n      return ErrorCategory.USER_INPUT;\r\n    }\r\n    if (\r\n      errorKey.includes('external') ||\r\n      errorKey.includes('service') ||\r\n      errorKey.includes('api')\r\n    ) {\r\n      return ErrorCategory.EXTERNAL_SERVICE;\r\n    }\r\n\r\n    return ErrorCategory.SYSTEM;\r\n  }\r\n\r\n  /**\r\n   * Determine error severity from custom error\r\n   */\r\n  private determineSeverity(error: any): ErrorSeverity {\r\n    const errorKey = error.error?.errorKey?.toLowerCase() || '';\r\n\r\n    if (errorKey.includes('critical') || errorKey.includes('fatal')) {\r\n      return ErrorSeverity.CRITICAL;\r\n    }\r\n    if (errorKey.includes('high') || errorKey.includes('severe')) {\r\n      return ErrorSeverity.HIGH;\r\n    }\r\n    if (errorKey.includes('low') || errorKey.includes('minor')) {\r\n      return ErrorSeverity.LOW;\r\n    }\r\n\r\n    return ErrorSeverity.MEDIUM;\r\n  }\r\n\r\n  /**\r\n   * Categorize HTTP error by status code\r\n   */\r\n  private categorizeHttpError(statusCode: number): ErrorCategory {\r\n    if (statusCode === 401) {\r\n      return ErrorCategory.AUTHENTICATION;\r\n    }\r\n    if (statusCode === 403) {\r\n      return ErrorCategory.AUTHORIZATION;\r\n    }\r\n    if (statusCode >= 400 && statusCode < 500) {\r\n      return ErrorCategory.VALIDATION;\r\n    }\r\n    if (statusCode >= 500) {\r\n      return ErrorCategory.SYSTEM;\r\n    }\r\n    if (statusCode === 0 || statusCode < 0) {\r\n      return ErrorCategory.NETWORK;\r\n    }\r\n\r\n    return ErrorCategory.SYSTEM;\r\n  }\r\n\r\n  /**\r\n   * Determine severity from HTTP status code\r\n   */\r\n  private determineSeverityFromStatus(statusCode: number): ErrorSeverity {\r\n    if (statusCode >= 500) {\r\n      return ErrorSeverity.HIGH;\r\n    }\r\n    if (statusCode === 401 || statusCode === 403) {\r\n      return ErrorSeverity.MEDIUM;\r\n    }\r\n    if (statusCode === 404) {\r\n      return ErrorSeverity.LOW;\r\n    }\r\n    if (statusCode >= 400 && statusCode < 500) {\r\n      return ErrorSeverity.MEDIUM;\r\n    }\r\n    if (statusCode === 0 || statusCode < 0) {\r\n      return ErrorSeverity.HIGH;\r\n    }\r\n\r\n    return ErrorSeverity.MEDIUM;\r\n  }\r\n\r\n  /**\r\n   * Check if error is retryable\r\n   */\r\n  private isRetryable(error: any, statusCode?: number): boolean {\r\n    // Network errors are usually retryable\r\n    if (statusCode === 0 || statusCode === undefined) {\r\n      return true;\r\n    }\r\n\r\n    // Server errors are retryable\r\n    if (statusCode >= 500) {\r\n      return true;\r\n    }\r\n\r\n    // Timeout errors are retryable\r\n    if (statusCode === 408 || statusCode === 504) {\r\n      return true;\r\n    }\r\n\r\n    // Rate limiting is retryable\r\n    if (statusCode === 429) {\r\n      return true;\r\n    }\r\n\r\n    // Client errors are generally not retryable\r\n    if (statusCode >= 400 && statusCode < 500) {\r\n      return false;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Convert enhanced error back to legacy format for backward compatibility\r\n   */\r\n  private convertToLegacyFormat(error: EnhancedError): any {\r\n    return {\r\n      ...error.originalError,\r\n      status: error.statusCode,\r\n      error: {\r\n        errorKey: error.errorKey,\r\n        message: error.message,\r\n        errorDetails: error.context.additionalData,\r\n      },\r\n      isCustomError: error.isCustomError,\r\n    };\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,EAAcC,OAAO,QAAQ,MAAM;AAC3D,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AAC9E,SAASC,WAAW,QAAQ,sCAAsC;;AAElE;;;AAGA,WAAYC,aAKX;AALD,WAAYA,aAAa;EACvBA,aAAA,eAAW;EACXA,aAAA,qBAAiB;EACjBA,aAAA,iBAAa;EACbA,aAAA,yBAAqB;AACvB,CAAC,EALWA,aAAa,KAAbA,aAAa;AAOzB;;;AAGA,WAAYC,aASX;AATD,WAAYA,aAAa;EACvBA,aAAA,uBAAmB;EACnBA,aAAA,qCAAiC;EACjCA,aAAA,mCAA+B;EAC/BA,aAAA,6BAAyB;EACzBA,aAAA,qCAAiC;EACjCA,aAAA,qBAAiB;EACjBA,aAAA,6BAAyB;EACzBA,aAAA,yCAAqC;AACvC,CAAC,EATWA,aAAa,KAAbA,aAAa;AAsFzB,OAAM,MAAOC,YAAY;EAavBC,YAAA;IAZiB,KAAAC,QAAQ,GAAG,IAAIT,OAAO,EAAQ;IAC9B,KAAAU,OAAO,GAAG,IAAIV,OAAO,EAAiB;IACtC,KAAAW,aAAa,GAAG,IAAIZ,eAAe,CAAkB,EAAE,CAAC;IACxD,KAAAa,WAAW,GAAG,IAAIb,eAAe,CAChD,IAAI,CAACc,oBAAoB,EAAE,CAC5B;IAEO,KAAAC,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,YAAY,GAAoB,EAAE;IAIxC,IAAI,CAACC,MAAM,GAAG;MACZC,aAAa,EAAE,CAACd,WAAW,CAACe,UAAU;MACtCC,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE,GAAG;MACpBpB,YAAY,EAAE,GAAG;MACjBqB,oBAAoB,EAAE,CAAC;MACvBC,QAAQ,EAAEnB,WAAW,CAACe,UAAU,GAAG,OAAO,GAAG;KAC9C;IAED,IAAI,CAACK,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChB,QAAQ,CAACiB,IAAI,EAAE;IACpB,IAAI,CAACjB,QAAQ,CAACkB,QAAQ,EAAE;IACxB,IAAI,CAACjB,OAAO,CAACiB,QAAQ,EAAE;IACvB,IAAI,CAAChB,aAAa,CAACgB,QAAQ,EAAE;IAC7B,IAAI,CAACf,WAAW,CAACe,QAAQ,EAAE;EAC7B;EAEA;;;EAGQH,uBAAuBA,CAAA;IAC7B,IAAI,CAACd,OAAO,CACTkB,IAAI,CACHzB,SAAS,CAAC,IAAI,CAACM,QAAQ,CAAC,EACxBR,YAAY,CAAC,IAAI,CAACgB,MAAM,CAAChB,YAAY,IAAI,GAAG,CAAC,EAC7CC,oBAAoB,CAClB,CAAC2B,IAAI,EAAEC,IAAI,KACTD,IAAI,CAACE,OAAO,KAAKD,IAAI,CAACC,OAAO,IAC7BF,IAAI,CAACG,UAAU,KAAKF,IAAI,CAACE,UAAU,IACnCC,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACK,SAAS,CAACC,OAAO,EAAE,GAAGP,IAAI,CAACM,SAAS,CAACC,OAAO,EAAE,CAAC,GAAG,IAAI,CACvE,CACF,CACAC,SAAS,CAAEC,KAAK,IAAI;MACnB,IAAI,CAACC,YAAY,CAACD,KAAK,CAAC;IAC1B,CAAC,CAAC;EACN;EAEA;;;EAGOE,QAAQA,CAACF,KAAU,EAAEG,OAA+B;IACzD,MAAMC,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAACL,KAAK,EAAEG,OAAO,CAAC;IAE9D,IAAI,IAAI,CAACG,mBAAmB,CAACF,aAAa,CAAC,EAAE;MAC3C,OAAOA,aAAa,CAACG,EAAE;;IAGzB,IAAI,CAACnC,OAAO,CAACgB,IAAI,CAACgB,aAAa,CAAC;IAChC,OAAOA,aAAa,CAACG,EAAE;EACzB;EAEA;;;EAGOC,cAAcA,CACnBf,OAAe,EACfgB,QAAA,GAA0BzC,aAAa,CAAC0C,MAAM,EAC9CC,QAAA,GAA0B5C,aAAa,CAAC6C,MAAM,EAC9CT,OAA+B,EAC/BU,OAIC;IAED,MAAMC,WAAW,GAAG;MAClBC,aAAa,EAAE,IAAI;MACnBtB,OAAO;MACPO,KAAK,EAAE;QACLgB,QAAQ,EAAEH,OAAO,EAAEG,QAAQ;QAC3BvB;OACD;MACD,GAAGoB;KACJ;IAED,OAAO,IAAI,CAACX,QAAQ,CAACY,WAAW,EAAE;MAChC,GAAGX,OAAO;MACVc,SAAS,EAAEd,OAAO,EAAEc,SAAS,IAAI;KAClC,CAAC;EACJ;EAEA;;;EAGOC,SAASA,CAACC,MAA6B;IAC5C,OAAO,IAAI,CAAC/C,OAAO,CAACkB,IAAI,CACtB6B,MAAM,CAAEnB,KAAK,IAAK,IAAI,CAACoB,aAAa,CAACpB,KAAK,EAAEmB,MAAM,CAAC,CAAC,EACpDtD,SAAS,CAAC,IAAI,CAACM,QAAQ,CAAC,CACzB;EACH;EAEA;;;EAGOkD,eAAeA,CAAA;IACpB,OAAO,IAAI,CAAChD,aAAa,CAACiD,YAAY,EAAE;EAC1C;EAEA;;;EAGOC,aAAaA,CAAA;IAClB,OAAO,IAAI,CAACjD,WAAW,CAACgD,YAAY,EAAE;EACxC;EAEA;;;EAGOE,2BAA2BA,CAACC,GAAW;IAC5C,IAAI,CAACjD,gBAAgB,CAACkD,IAAI,CAACD,GAAG,CAAC;EACjC;EAEA;;;EAGOE,oBAAoBA,CAACF,GAAW;IACrC,OAAO,IAAI,CAACjD,gBAAgB,CAACoD,IAAI,CAAEC,EAAE,IAAKJ,GAAG,CAACK,QAAQ,CAACD,EAAE,CAAC,CAAC;EAC7D;EAEA;;;EAGOE,cAAcA,CAACZ,MAAmB;IACvC,IAAI,CAAC1C,YAAY,CAACiD,IAAI,CAACP,MAAM,CAAC;EAChC;EAEA;;;EAGOa,iBAAiBA,CAACb,MAAmB;IAC1C,MAAMc,KAAK,GAAG,IAAI,CAACxD,YAAY,CAACyD,OAAO,CAACf,MAAM,CAAC;IAC/C,IAAIc,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACxD,YAAY,CAAC0D,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;;EAEtC;EAEA;;;EAGOG,iBAAiBA,CAAA;IACtB,IAAI,CAAC3D,YAAY,GAAG,EAAE;EACxB;EAEA;;;EAGO4D,YAAYA,CAAC1D,MAAmC;IACrD,IAAI,CAACA,MAAM,GAAG;MAAE,GAAG,IAAI,CAACA,MAAM;MAAE,GAAGA;IAAM,CAAE;EAC7C;EAEA;;;EAGO2D,iBAAiBA,CAAA;IACtB,IAAI,CAAC5D,YAAY,GAAG,EAAE;IACtB,IAAI,CAACL,aAAa,CAACe,IAAI,CAAC,EAAE,CAAC;EAC7B;EAEA;;;EAGOmD,kBAAkBA,CAACC,OAAe;IACvC,MAAMxC,KAAK,GAAG,IAAI,CAACtB,YAAY,CAAC+D,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACnC,EAAE,KAAKiC,OAAO,CAAC;IAC7D,IAAIxC,KAAK,EAAE;MACTA,KAAK,CAAC2C,OAAO,GAAG,IAAI;MACpB,IAAI,CAACtE,aAAa,CAACe,IAAI,CAAC,CAAC,GAAG,IAAI,CAACV,YAAY,CAAC,CAAC;;EAEnD;EAEA;;;EAGOkE,UAAUA,CACfJ,OAAe,EACfK,aAAoC;IAEpC,MAAM7C,KAAK,GAAG,IAAI,CAACtB,YAAY,CAAC+D,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACnC,EAAE,KAAKiC,OAAO,CAAC;IAC7D,IAAI,CAACxC,KAAK,IAAI,CAACA,KAAK,CAAC8C,SAAS,EAAE;MAC9B,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;;IAGxD/C,KAAK,CAACgD,UAAU,GAAG,CAAChD,KAAK,CAACgD,UAAU,IAAI,CAAC,IAAI,CAAC;IAE9C,IACEhD,KAAK,CAACgD,UAAU,IACfhD,KAAK,CAACiD,UAAU,IAAI,IAAI,CAACtE,MAAM,CAACK,oBAAoB,IAAI,CAAC,CAAC,EAC3D;MACA,MAAM,IAAI+D,KAAK,CAAC,iCAAiC,CAAC;;IAGpD,OAAOF,aAAa,EAAE;EACxB;EAEA;;;EAGQtE,oBAAoBA,CAAA;IAC1B,OAAO;MACL2E,WAAW,EAAE,CAAC;MACdC,gBAAgB,EAAE,EAAmC;MACrDC,gBAAgB,EAAE,EAAmC;MACrDC,kBAAkB,EAAE,EAAE;MACtBC,gBAAgB,EAAE,EAAE;MACpBC,WAAW,EAAE;KACd;EACH;EAEA;;;EAGQlD,mBAAmBA,CACzBL,KAAU,EACVG,OAA+B;IAE/B,MAAMN,SAAS,GAAG,IAAI2D,IAAI,EAAE;IAC5B,MAAMjD,EAAE,GAAG,IAAI,CAACkD,eAAe,EAAE;IAEjC;IACA,MAAMC,WAAW,GAAG1D,KAAK,EAAE2D,MAAM,KAAKC,SAAS;IAC/C,MAAM7C,aAAa,GAAGf,KAAK,EAAEe,aAAa,KAAK,IAAI;IAEnD,IAAItB,OAAO,GAAG,EAAE;IAChB,IAAIC,UAA8B;IAClC,IAAIsB,QAA4B;IAChC,IAAIP,QAAQ,GAAGzC,aAAa,CAAC0C,MAAM;IACnC,IAAIC,QAAQ,GAAG5C,aAAa,CAAC6C,MAAM;IAEnC,IAAIG,aAAa,EAAE;MACjBtB,OAAO,GAAGO,KAAK,CAACP,OAAO,IAAI,uBAAuB;MAClDuB,QAAQ,GAAGhB,KAAK,CAACA,KAAK,EAAEgB,QAAQ;MAChCP,QAAQ,GAAG,IAAI,CAACoD,iBAAiB,CAAC7D,KAAK,CAAC;MACxCW,QAAQ,GAAG,IAAI,CAACmD,iBAAiB,CAAC9D,KAAK,CAAC;KACzC,MAAM,IAAI0D,WAAW,EAAE;MACtBhE,UAAU,GAAGM,KAAK,CAAC2D,MAAM;MACzBlE,OAAO,GACLO,KAAK,CAACA,KAAK,EAAEP,OAAO,IAAIO,KAAK,CAACP,OAAO,IAAI,QAAQC,UAAU,QAAQ;MACrEsB,QAAQ,GAAGhB,KAAK,CAACA,KAAK,EAAEgB,QAAQ;MAChCP,QAAQ,GAAG,IAAI,CAACsD,mBAAmB,CAACrE,UAAU,CAAC;MAC/CiB,QAAQ,GAAG,IAAI,CAACqD,2BAA2B,CAACtE,UAAU,CAAC;KACxD,MAAM;MACLD,OAAO,GAAGO,KAAK,EAAEP,OAAO,IAAI,wBAAwB;MACpDgB,QAAQ,GAAGzC,aAAa,CAAC0C,MAAM;MAC/BC,QAAQ,GAAG5C,aAAa,CAACkG,IAAI;;IAG/B,MAAM7D,aAAa,GAAkB;MACnCG,EAAE;MACF2D,aAAa,EAAElE,KAAK;MACpBP,OAAO;MACPkB,QAAQ;MACRF,QAAQ;MACRN,OAAO,EAAE;QACPN,SAAS;QACT4B,GAAG,EAAE0C,MAAM,CAACC,QAAQ,CAACC,IAAI;QACzBC,SAAS,EAAEC,SAAS,CAACD,SAAS;QAC9B,GAAGnE;OACJ;MACDY,aAAa;MACbC,QAAQ;MACRtB,UAAU;MACVoD,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAACxE,KAAK,EAAEN,UAAU,CAAC;MAC9CsD,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,IAAI,CAACtE,MAAM,CAACK,oBAAoB;MAC5CyF,cAAc,EAAEzE,KAAK,EAAEyE,cAAc,IAAI,KAAK;MAC9C9B,OAAO,EAAE,KAAK;MACd9C;KACD;IAED,OAAOO,aAAa;EACtB;EAEA;;;EAGQH,YAAYA,CAACD,KAAoB;IACvC;IACA,IAAI,CAAC0E,YAAY,CAAC1E,KAAK,CAAC;IAExB;IACA,IAAI,CAAC2E,gBAAgB,CAAC3E,KAAK,CAAC;IAE5B;IACA,IAAI,IAAI,CAACrB,MAAM,CAACC,aAAa,IAAI,CAAC,IAAI,CAACgG,qBAAqB,CAAC5E,KAAK,CAAC,EAAE;MACnE,IAAI,CAAC6E,QAAQ,CAAC7E,KAAK,CAAC;;IAGtB;IACA,IAAI,CAACA,KAAK,CAACyE,cAAc,EAAE;MACzB;MACA,MAAMK,WAAW,GAAG,IAAI,CAACC,qBAAqB,CAAC/E,KAAK,CAAC;MACrD;MACA;;EAEJ;EAEA;;;EAGQM,mBAAmBA,CAACN,KAAoB;IAC9C;IACA,IAAIA,KAAK,CAACG,OAAO,CAACsB,GAAG,IAAI,IAAI,CAACE,oBAAoB,CAAC3B,KAAK,CAACG,OAAO,CAACsB,GAAG,CAAC,EAAE;MACrE,OAAO,IAAI;;IAGb;IACA,OAAO,IAAI,CAAChD,YAAY,CAACmD,IAAI,CAAET,MAAM,IACnC,IAAI,CAACC,aAAa,CAACpB,KAAK,EAAEmB,MAAM,CAAC,CAClC;EACH;EAEA;;;EAGQC,aAAaA,CACnBpB,KAAoB,EACpBmB,MAA6B;IAE7B,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;IAExB,IACEA,MAAM,CAAC6D,WAAW,IAClBhF,KAAK,CAACN,UAAU,IAChB,CAACyB,MAAM,CAAC6D,WAAW,CAAClD,QAAQ,CAAC9B,KAAK,CAACN,UAAU,CAAC,EAC9C;MACA,OAAO,KAAK;;IAGd,IACEyB,MAAM,CAAC8D,SAAS,IAChBjF,KAAK,CAACgB,QAAQ,IACd,CAACG,MAAM,CAAC8D,SAAS,CAACnD,QAAQ,CAAC9B,KAAK,CAACgB,QAAQ,CAAC,EAC1C;MACA,OAAO,KAAK;;IAGd,IAAIG,MAAM,CAAC+D,UAAU,IAAI,CAAC/D,MAAM,CAAC+D,UAAU,CAACpD,QAAQ,CAAC9B,KAAK,CAACS,QAAQ,CAAC,EAAE;MACpE,OAAO,KAAK;;IAGd,IAAIU,MAAM,CAACgE,UAAU,IAAI,CAAChE,MAAM,CAACgE,UAAU,CAACrD,QAAQ,CAAC9B,KAAK,CAACW,QAAQ,CAAC,EAAE;MACpE,OAAO,KAAK;;IAGd,IAAIQ,MAAM,CAACiE,WAAW,IAAIpF,KAAK,CAACG,OAAO,CAACsB,GAAG,EAAE;MAC3C,MAAM4D,UAAU,GAAGlE,MAAM,CAACiE,WAAW,CAACxD,IAAI,CAAE0D,OAAO,IACjDtF,KAAK,CAACG,OAAO,CAACsB,GAAI,CAACK,QAAQ,CAACwD,OAAO,CAAC,CACrC;MACD,IAAI,CAACD,UAAU,EAAE,OAAO,KAAK;;IAG/B,OAAO,IAAI;EACb;EAEA;;;EAGQT,qBAAqBA,CAAC5E,KAAoB;IAChD,OAAO,IAAI,CAACvB,YAAY,CAACmD,IAAI,CAC1BT,MAAM,IAAKA,MAAM,CAACoE,eAAe,IAAI,IAAI,CAACnE,aAAa,CAACpB,KAAK,EAAEmB,MAAM,CAAC,CACxE;EACH;EAEA;;;EAGQuD,YAAYA,CAAC1E,KAAoB;IACvC,IAAI,CAACtB,YAAY,CAAC8G,OAAO,CAACxF,KAAK,CAAC;IAEhC;IACA,IAAI,IAAI,CAACtB,YAAY,CAAC+G,MAAM,IAAI,IAAI,CAAC9G,MAAM,CAACI,eAAe,IAAI,GAAG,CAAC,EAAE;MACnE,IAAI,CAACL,YAAY,GAAG,IAAI,CAACA,YAAY,CAACgH,KAAK,CACzC,CAAC,EACD,IAAI,CAAC/G,MAAM,CAACI,eAAe,IAAI,GAAG,CACnC;;IAGH,IAAI,CAACV,aAAa,CAACe,IAAI,CAAC,CAAC,GAAG,IAAI,CAACV,YAAY,CAAC,CAAC;EACjD;EAEA;;;EAGQiG,gBAAgBA,CAAC3E,KAAoB;IAC3C,MAAM2F,YAAY,GAAG,IAAI,CAACrH,WAAW,CAACsH,KAAK;IAE3C;IACAD,YAAY,CAACzC,WAAW,EAAE;IAE1B;IACAyC,YAAY,CAACxC,gBAAgB,CAACnD,KAAK,CAACS,QAAQ,CAAC,GAC3C,CAACkF,YAAY,CAACxC,gBAAgB,CAACnD,KAAK,CAACS,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAE1D;IACAkF,YAAY,CAACvC,gBAAgB,CAACpD,KAAK,CAACW,QAAQ,CAAC,GAC3C,CAACgF,YAAY,CAACvC,gBAAgB,CAACpD,KAAK,CAACW,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAE1D;IACA,IAAIX,KAAK,CAACN,UAAU,EAAE;MACpBiG,YAAY,CAACtC,kBAAkB,CAACrD,KAAK,CAACN,UAAU,CAAC,GAC/C,CAACiG,YAAY,CAACtC,kBAAkB,CAACrD,KAAK,CAACN,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;IAGhE;IACA,IAAIM,KAAK,CAACgB,QAAQ,EAAE;MAClB,MAAM6E,aAAa,GAAGF,YAAY,CAACrC,gBAAgB,CAACb,IAAI,CACrDC,CAAC,IAAKA,CAAC,CAAC1B,QAAQ,KAAKhB,KAAK,CAACgB,QAAQ,CACrC;MACD,IAAI6E,aAAa,EAAE;QACjBA,aAAa,CAACC,KAAK,EAAE;OACtB,MAAM;QACLH,YAAY,CAACrC,gBAAgB,CAAC5B,IAAI,CAAC;UACjCV,QAAQ,EAAEhB,KAAK,CAACgB,QAAQ;UACxB8E,KAAK,EAAE;SACR,CAAC;;MAGJ;MACAH,YAAY,CAACrC,gBAAgB,CAACyC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACH,KAAK,GAAGE,CAAC,CAACF,KAAK,CAAC;MAC/DH,YAAY,CAACrC,gBAAgB,GAAGqC,YAAY,CAACrC,gBAAgB,CAACoC,KAAK,CACjE,CAAC,EACD,EAAE,CACH;;IAGH;IACA,MAAMQ,UAAU,GAAG,IAAI1C,IAAI,CAACxD,KAAK,CAACH,SAAS,CAAC;IAC5CqG,UAAU,CAACC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE9B,MAAMC,aAAa,GAAGT,YAAY,CAACpC,WAAW,CAACd,IAAI,CAChD4D,CAAC,IAAKA,CAAC,CAACxG,SAAS,CAACC,OAAO,EAAE,KAAKoG,UAAU,CAACpG,OAAO,EAAE,CACtD;IAED,IAAIsG,aAAa,EAAE;MACjBA,aAAa,CAACN,KAAK,EAAE;KACtB,MAAM;MACLH,YAAY,CAACpC,WAAW,CAAC7B,IAAI,CAAC;QAAE7B,SAAS,EAAEqG,UAAU;QAAEJ,KAAK,EAAE;MAAC,CAAE,CAAC;;IAGpE;IACA,MAAMQ,SAAS,GAAG,IAAI9C,IAAI,CAACA,IAAI,CAAC+C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAC5DZ,YAAY,CAACpC,WAAW,GAAGoC,YAAY,CAACpC,WAAW,CAACpC,MAAM,CACvDkF,CAAC,IAAKA,CAAC,CAACxG,SAAS,IAAIyG,SAAS,CAChC;IAED,IAAI,CAAChI,WAAW,CAACc,IAAI,CAACuG,YAAY,CAAC;EACrC;EAEA;;;EAGQd,QAAQA,CAAC7E,KAAoB;IACnC,MAAMf,QAAQ,GAAG,IAAI,CAACN,MAAM,CAACM,QAAQ,IAAI,OAAO;IAChD,MAAMuH,UAAU,GAAG,IAAIxG,KAAK,CAACW,QAAQ,CAAC8F,WAAW,EAAE,KAAKzG,KAAK,CAACS,QAAQ,KACpET,KAAK,CAACP,OACR,EAAE;IACF,MAAMiH,OAAO,GAAG;MACdnG,EAAE,EAAEP,KAAK,CAACO,EAAE;MACZE,QAAQ,EAAET,KAAK,CAACS,QAAQ;MACxBE,QAAQ,EAAEX,KAAK,CAACW,QAAQ;MACxBjB,UAAU,EAAEM,KAAK,CAACN,UAAU;MAC5BsB,QAAQ,EAAEhB,KAAK,CAACgB,QAAQ;MACxBb,OAAO,EAAEH,KAAK,CAACG,OAAO;MACtB+D,aAAa,EAAElE,KAAK,CAACkE;KACtB;IAED,QAAQjF,QAAQ;MACd,KAAK,OAAO;QACV0H,OAAO,CAACC,KAAK,CAACJ,UAAU,EAAEE,OAAO,CAAC;QAClC;MACF,KAAK,MAAM;QACT,IAAI1G,KAAK,CAACW,QAAQ,KAAK5C,aAAa,CAAC8I,GAAG,EAAE;UACxCF,OAAO,CAACG,IAAI,CAACN,UAAU,EAAEE,OAAO,CAAC;SAClC,MAAM;UACLC,OAAO,CAAC3G,KAAK,CAACwG,UAAU,EAAEE,OAAO,CAAC;;QAEpC;MACF,KAAK,MAAM;QACT,IACE1G,KAAK,CAACW,QAAQ,KAAK5C,aAAa,CAAC8I,GAAG,IACpC7G,KAAK,CAACW,QAAQ,KAAK5C,aAAa,CAAC6C,MAAM,EACvC;UACA+F,OAAO,CAACI,IAAI,CAACP,UAAU,EAAEE,OAAO,CAAC;SAClC,MAAM;UACLC,OAAO,CAAC3G,KAAK,CAACwG,UAAU,EAAEE,OAAO,CAAC;;QAEpC;MACF,KAAK,OAAO;MACZ;QACEC,OAAO,CAAC3G,KAAK,CAACwG,UAAU,EAAEE,OAAO,CAAC;QAClC;;EAEN;EAEA;;;EAGQjD,eAAeA,CAAA;IACrB,OAAO,SAASD,IAAI,CAAC+C,GAAG,EAAE,IAAI5G,IAAI,CAACqH,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACzE;EAEA;;;EAGQrD,iBAAiBA,CAAC7D,KAAU;IAClC,MAAMgB,QAAQ,GAAGhB,KAAK,CAACA,KAAK,EAAEgB,QAAQ,EAAEmG,WAAW,EAAE,IAAI,EAAE;IAE3D,IACEnG,QAAQ,CAACc,QAAQ,CAAC,MAAM,CAAC,IACzBd,QAAQ,CAACc,QAAQ,CAAC,OAAO,CAAC,IAC1Bd,QAAQ,CAACc,QAAQ,CAAC,OAAO,CAAC,EAC1B;MACA,OAAO9D,aAAa,CAACoJ,cAAc;;IAErC,IACEpG,QAAQ,CAACc,QAAQ,CAAC,YAAY,CAAC,IAC/Bd,QAAQ,CAACc,QAAQ,CAAC,QAAQ,CAAC,IAC3Bd,QAAQ,CAACc,QAAQ,CAAC,WAAW,CAAC,EAC9B;MACA,OAAO9D,aAAa,CAACqJ,aAAa;;IAEpC,IACErG,QAAQ,CAACc,QAAQ,CAAC,YAAY,CAAC,IAC/Bd,QAAQ,CAACc,QAAQ,CAAC,SAAS,CAAC,IAC5Bd,QAAQ,CAACc,QAAQ,CAAC,UAAU,CAAC,EAC7B;MACA,OAAO9D,aAAa,CAACsJ,UAAU;;IAEjC,IACEtG,QAAQ,CAACc,QAAQ,CAAC,UAAU,CAAC,IAC7Bd,QAAQ,CAACc,QAAQ,CAAC,MAAM,CAAC,IACzBd,QAAQ,CAACc,QAAQ,CAAC,OAAO,CAAC,EAC1B;MACA,OAAO9D,aAAa,CAACuJ,cAAc;;IAErC,IAAIvG,QAAQ,CAACc,QAAQ,CAAC,OAAO,CAAC,IAAId,QAAQ,CAACc,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC3D,OAAO9D,aAAa,CAACwJ,UAAU;;IAEjC,IACExG,QAAQ,CAACc,QAAQ,CAAC,UAAU,CAAC,IAC7Bd,QAAQ,CAACc,QAAQ,CAAC,SAAS,CAAC,IAC5Bd,QAAQ,CAACc,QAAQ,CAAC,KAAK,CAAC,EACxB;MACA,OAAO9D,aAAa,CAACyJ,gBAAgB;;IAGvC,OAAOzJ,aAAa,CAAC0C,MAAM;EAC7B;EAEA;;;EAGQoD,iBAAiBA,CAAC9D,KAAU;IAClC,MAAMgB,QAAQ,GAAGhB,KAAK,CAACA,KAAK,EAAEgB,QAAQ,EAAEmG,WAAW,EAAE,IAAI,EAAE;IAE3D,IAAInG,QAAQ,CAACc,QAAQ,CAAC,UAAU,CAAC,IAAId,QAAQ,CAACc,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC/D,OAAO/D,aAAa,CAAC2J,QAAQ;;IAE/B,IAAI1G,QAAQ,CAACc,QAAQ,CAAC,MAAM,CAAC,IAAId,QAAQ,CAACc,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC5D,OAAO/D,aAAa,CAACkG,IAAI;;IAE3B,IAAIjD,QAAQ,CAACc,QAAQ,CAAC,KAAK,CAAC,IAAId,QAAQ,CAACc,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC1D,OAAO/D,aAAa,CAAC8I,GAAG;;IAG1B,OAAO9I,aAAa,CAAC6C,MAAM;EAC7B;EAEA;;;EAGQmD,mBAAmBA,CAACrE,UAAkB;IAC5C,IAAIA,UAAU,KAAK,GAAG,EAAE;MACtB,OAAO1B,aAAa,CAACoJ,cAAc;;IAErC,IAAI1H,UAAU,KAAK,GAAG,EAAE;MACtB,OAAO1B,aAAa,CAACqJ,aAAa;;IAEpC,IAAI3H,UAAU,IAAI,GAAG,IAAIA,UAAU,GAAG,GAAG,EAAE;MACzC,OAAO1B,aAAa,CAACsJ,UAAU;;IAEjC,IAAI5H,UAAU,IAAI,GAAG,EAAE;MACrB,OAAO1B,aAAa,CAAC0C,MAAM;;IAE7B,IAAIhB,UAAU,KAAK,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;MACtC,OAAO1B,aAAa,CAAC2J,OAAO;;IAG9B,OAAO3J,aAAa,CAAC0C,MAAM;EAC7B;EAEA;;;EAGQsD,2BAA2BA,CAACtE,UAAkB;IACpD,IAAIA,UAAU,IAAI,GAAG,EAAE;MACrB,OAAO3B,aAAa,CAACkG,IAAI;;IAE3B,IAAIvE,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,GAAG,EAAE;MAC5C,OAAO3B,aAAa,CAAC6C,MAAM;;IAE7B,IAAIlB,UAAU,KAAK,GAAG,EAAE;MACtB,OAAO3B,aAAa,CAAC8I,GAAG;;IAE1B,IAAInH,UAAU,IAAI,GAAG,IAAIA,UAAU,GAAG,GAAG,EAAE;MACzC,OAAO3B,aAAa,CAAC6C,MAAM;;IAE7B,IAAIlB,UAAU,KAAK,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;MACtC,OAAO3B,aAAa,CAACkG,IAAI;;IAG3B,OAAOlG,aAAa,CAAC6C,MAAM;EAC7B;EAEA;;;EAGQ4D,WAAWA,CAACxE,KAAU,EAAEN,UAAmB;IACjD;IACA,IAAIA,UAAU,KAAK,CAAC,IAAIA,UAAU,KAAKkE,SAAS,EAAE;MAChD,OAAO,IAAI;;IAGb;IACA,IAAIlE,UAAU,IAAI,GAAG,EAAE;MACrB,OAAO,IAAI;;IAGb;IACA,IAAIA,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,GAAG,EAAE;MAC5C,OAAO,IAAI;;IAGb;IACA,IAAIA,UAAU,KAAK,GAAG,EAAE;MACtB,OAAO,IAAI;;IAGb;IACA,IAAIA,UAAU,IAAI,GAAG,IAAIA,UAAU,GAAG,GAAG,EAAE;MACzC,OAAO,KAAK;;IAGd,OAAO,KAAK;EACd;EAEA;;;EAGQqF,qBAAqBA,CAAC/E,KAAoB;IAChD,OAAO;MACL,GAAGA,KAAK,CAACkE,aAAa;MACtBP,MAAM,EAAE3D,KAAK,CAACN,UAAU;MACxBM,KAAK,EAAE;QACLgB,QAAQ,EAAEhB,KAAK,CAACgB,QAAQ;QACxBvB,OAAO,EAAEO,KAAK,CAACP,OAAO;QACtBmI,YAAY,EAAE5H,KAAK,CAACG,OAAO,CAAC0H;OAC7B;MACD9G,aAAa,EAAEf,KAAK,CAACe;KACtB;EACH;EAAC,QAAA+G,CAAA,G;qBApqBU7J,YAAY;EAAA;EAAA,QAAA8J,EAAA,G;WAAZ9J,YAAY;IAAA+J,OAAA,EAAZ/J,YAAY,CAAAgK,IAAA;IAAAC,UAAA,EAFX;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}