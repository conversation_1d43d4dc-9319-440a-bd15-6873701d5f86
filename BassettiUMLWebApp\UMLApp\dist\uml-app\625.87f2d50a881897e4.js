"use strict";(self.webpackChunkUmlApp=self.webpackChunkUmlApp||[]).push([[625],{8625:(Do,_e,l)=>{l.r(_e),l.d(_e,{HomeModule:()=>Mo});var m=l(6223),R=l(2130),Ze=l(590),Ve=l(3815),S=l(1365),e=l(5879),v=l(6814);class O{r;g;b;a;constructor(s,t,i,o){this.r=s,this.g=t,this.b=i,this.a=o}}class ne{h;s;v;a;constructor(s,t,i,o){this.h=s,this.s=t,this.v=i,this.a=o}}class F{h;s;l;a;constructor(s,t,i,o){this.h=s,this.s=t,this.l=i,this.a=o}}class K{c;m;y;k;a;constructor(s,t,i,o,r=1){this.c=s,this.m=t,this.y=i,this.k=o,this.a=r}}let tt=(()=>{class n{active=null;setActive(t){this.active&&this.active!==t&&"inline"!==this.active.cpDialogDisplay&&this.active.closeDialog(),this.active=t}hsva2hsla(t){const i=t.h,o=t.s,r=t.v,a=t.a;if(0===r)return new F(i,0,0,a);if(0===o&&1===r)return new F(i,1,1,a);{const c=r*(2-o)/2;return new F(i,r*o/(1-Math.abs(2*c-1)),c,a)}}hsla2hsva(t){const i=Math.min(t.h,1),o=Math.min(t.s,1),r=Math.min(t.l,1),a=Math.min(t.a,1);if(0===r)return new ne(i,0,0,a);{const c=r+o*(1-Math.abs(2*r-1))/2;return new ne(i,2*(c-r)/c,c,a)}}hsvaToRgba(t){let i,o,r;const a=t.h,c=t.s,d=t.v,g=t.a,C=Math.floor(6*a),f=6*a-C,h=d*(1-c),_=d*(1-f*c),b=d*(1-(1-f)*c);switch(C%6){case 0:i=d,o=b,r=h;break;case 1:i=_,o=d,r=h;break;case 2:i=h,o=d,r=b;break;case 3:i=h,o=_,r=d;break;case 4:i=b,o=h,r=d;break;case 5:i=d,o=h,r=_;break;default:i=0,o=0,r=0}return new O(i,o,r,g)}cmykToRgb(t){return new O((1-t.c)*(1-t.k),(1-t.m)*(1-t.k),(1-t.y)*(1-t.k),t.a)}rgbaToCmyk(t){const i=1-Math.max(t.r,t.g,t.b);return 1===i?new K(0,0,0,1,t.a):new K((1-t.r-i)/(1-i),(1-t.g-i)/(1-i),(1-t.b-i)/(1-i),i,t.a)}rgbaToHsva(t){let i,o;const r=Math.min(t.r,1),a=Math.min(t.g,1),c=Math.min(t.b,1),d=Math.min(t.a,1),g=Math.max(r,a,c),C=Math.min(r,a,c),f=g,h=g-C;if(o=0===g?0:h/g,g===C)i=0;else{switch(g){case r:i=(a-c)/h+(a<c?6:0);break;case a:i=(c-r)/h+2;break;case c:i=(r-a)/h+4;break;default:i=0}i/=6}return new ne(i,o,f,d)}rgbaToHex(t,i){let o="#"+(16777216|t.r<<16|t.g<<8|t.b).toString(16).substr(1);return i&&(o+=(256|Math.round(255*t.a)).toString(16).substr(1)),o}normalizeCMYK(t){return new K(t.c/100,t.m/100,t.y/100,t.k/100,t.a)}denormalizeCMYK(t){return new K(Math.floor(100*t.c),Math.floor(100*t.m),Math.floor(100*t.y),Math.floor(100*t.k),t.a)}denormalizeRGBA(t){return new O(Math.round(255*t.r),Math.round(255*t.g),Math.round(255*t.b),t.a)}stringToHsva(t="",i=!1){let o=null;t=(t||"").toLowerCase();const r=[{re:/(rgb)a?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*%?,\s*(\d{1,3})\s*%?(?:,\s*(\d+(?:\.\d+)?)\s*)?\)/,parse:function(a){return new O(parseInt(a[2],10)/255,parseInt(a[3],10)/255,parseInt(a[4],10)/255,isNaN(parseFloat(a[5]))?1:parseFloat(a[5]))}},{re:/(hsl)a?\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*(?:,\s*(\d+(?:\.\d+)?)\s*)?\)/,parse:function(a){return new F(parseInt(a[2],10)/360,parseInt(a[3],10)/100,parseInt(a[4],10)/100,isNaN(parseFloat(a[5]))?1:parseFloat(a[5]))}}];r.push(i?{re:/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})?$/,parse:function(a){return new O(parseInt(a[1],16)/255,parseInt(a[2],16)/255,parseInt(a[3],16)/255,parseInt(a[4]||"FF",16)/255)}}:{re:/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})$/,parse:function(a){return new O(parseInt(a[1],16)/255,parseInt(a[2],16)/255,parseInt(a[3],16)/255,1)}}),r.push({re:/#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])$/,parse:function(a){return new O(parseInt(a[1]+a[1],16)/255,parseInt(a[2]+a[2],16)/255,parseInt(a[3]+a[3],16)/255,1)}});for(const a in r)if(r.hasOwnProperty(a)){const c=r[a],d=c.re.exec(t),g=d&&c.parse(d);if(g)return g instanceof O?o=this.rgbaToHsva(g):g instanceof F&&(o=this.hsla2hsva(g)),o}return o}outputFormat(t,i,o){switch("auto"===i&&(i=t.a<1?"rgba":"hex"),i){case"hsla":const r=this.hsva2hsla(t),a=new F(Math.round(360*r.h),Math.round(100*r.s),Math.round(100*r.l),Math.round(100*r.a)/100);return t.a<1||"always"===o?"hsla("+a.h+","+a.s+"%,"+a.l+"%,"+a.a+")":"hsl("+a.h+","+a.s+"%,"+a.l+"%)";case"rgba":const c=this.denormalizeRGBA(this.hsvaToRgba(t));return t.a<1||"always"===o?"rgba("+c.r+","+c.g+","+c.b+","+Math.round(100*c.a)/100+")":"rgb("+c.r+","+c.g+","+c.b+")";default:const d="always"===o||"forced"===o;return this.rgbaToHex(this.denormalizeRGBA(this.hsvaToRgba(t)),d)}}static \u0275fac=function(i){return new(i||n)};static \u0275prov=e.Yz7({token:n,factory:n.\u0275fac})}return n})();typeof window<"u"&&window;let it=(()=>{class n{static \u0275fac=function(i){return new(i||n)};static \u0275mod=e.oAB({type:n});static \u0275inj=e.cJS({providers:[tt],imports:[v.ez]})}return n})();var ot=l(3146),nt=l(1247),M=l(1896),re=l(7398),E=l(6306),rt=l(2096),x=l(8403),Ce=l(5449),B=l(5193),at=l(9862);let ve=(()=>{class n{constructor(t){this.http=t,this.backendUrl=Ce.N.backEndUrl}givePermission(t){return this.http.post(this.backendUrl+"/Permission",t,{context:(0,B.FK)("default")}).pipe((0,E.K)(i=>{throw console.error("Error giving the permission:",i),i}))}removePermission(t){return this.http.delete(this.backendUrl+`/Permission/${t}`,{context:(0,B.FK)("default")}).pipe((0,E.K)(i=>{throw console.error("Error removing the permission:",i),i}))}updatePermission(t){return this.http.patch(this.backendUrl+"/Permission",t,{context:(0,B.FK)("default")}).pipe((0,E.K)(i=>{throw console.error("Error updating the permission:",i),i}))}getProjectPermissions(t){return this.http.get(this.backendUrl+`/Project/permissions/${t}`,{context:(0,B.FK)("default")}).pipe((0,E.K)(i=>{throw console.error("Error getting the permissions:",i),i}))}checkPermission(t){return this.http.get(this.backendUrl+`/Project/checkpermission/${t}`,{context:(0,B.FK)("default")}).pipe((0,E.K)(i=>{throw console.error("Error getting the permissions:",i),i}))}static#e=this.\u0275fac=function(i){return new(i||n)(e.LFG(at.eN))};static#t=this.\u0275prov=e.Yz7({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var st=l(7195),ae=l(1476),q=l(3566),w=l(5313),z=l(8645),I=l(9773),xe=l(836),se=l(3620),lt=l(4716),dt=l(8504),pt=l(4825),Q=l(1818),W=l(5623),H=l(5886),J=l(2296),D=l(4170),U=l(3680),X=l(8525),ht=l(2599),gt=l(7548);function ut(n,s){if(1&n&&(e.TgZ(0,"mat-option",9),e.ALo(1,"splitUnderscore"),e._uU(2),e.ALo(3,"splitUnderscore"),e.qZA()),2&n){const t=s.$implicit;e.Q6J("value",e.lcZ(1,2,t.value)),e.xp6(2),e.hij(" ",e.lcZ(3,4,t.label)," ")}}function mt(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",5)(1,"mat-form-field",6),e.NdJ("click",function(o){return o.stopPropagation()}),e.TgZ(2,"mat-label"),e._uU(3),e.ALo(4,"translate"),e.qZA(),e.TgZ(5,"mat-select",7),e.NdJ("selectionChange",function(){e.CHM(t);const o=e.oxw().$implicit;return e.KtG(o.onApply(o))})("click",function(o){return o.stopPropagation()}),e.ALo(6,"translate"),e.YNc(7,ut,4,6,"mat-option",8),e.qZA()()()}if(2&n){const t=e.oxw().$implicit;let i;e.xp6(3),e.Oqu(e.lcZ(4,4,t.title)),e.xp6(2),e.Q6J("formControl",t.formControl)("placeholder",e.lcZ(6,6,null!==(i=t.placeholder)&&void 0!==i?i:"")),e.xp6(2),e.Q6J("ngForOf",t.options)}}function ft(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",10)(1,"mat-slide-toggle",11),e.NdJ("change",function(){e.CHM(t);const o=e.oxw().$implicit;return e.KtG(o.onApply(o))}),e._uU(2),e.ALo(3,"translate"),e.qZA(),e.TgZ(4,"button",12),e.NdJ("click",function(){e.CHM(t);const o=e.oxw(2);return e.KtG(o.resetFilters())}),e._uU(5),e.ALo(6,"translate"),e.qZA()()}if(2&n){const t=e.oxw().$implicit;e.xp6(1),e.Q6J("formControl",t.formControl),e.xp6(1),e.Oqu(e.lcZ(3,3,t.title)),e.xp6(3),e.hij(" ",e.lcZ(6,5,"dashboard.filter.clearFilter")," ")}}function _t(n,s){if(1&n&&(e.TgZ(0,"div",2),e.YNc(1,mt,8,8,"div",3),e.YNc(2,ft,7,7,"div",4),e.qZA()),2&n){const t=s.$implicit,i=e.oxw();e.xp6(1),e.Q6J("ngIf",t.type==i.inputType.SELECT),e.xp6(1),e.Q6J("ngIf",t.type==i.inputType.Toggle)}}let be=(()=>{class n{constructor(t,i,o){this.searchBarService=t,this.projectService=i,this._cdr=o,this.filters=[],this._destroy$=new z.x,this.isChecked=!1,this.inputType=x.vA,this.noOfFilter=0}ngAfterViewInit(){this.searchBarService.getFilters$().pipe((0,I.R)(this._destroy$)).subscribe(t=>{this.filters=[...t],this._cdr.detectChanges()})}resetFilters(){this.filters.forEach(t=>{t.formControl.reset(),t.type===x.vA.Toggle&&t.formControl.setValue(!1)}),this.searchBarService.filterCount.set(0),this.searchBarService.activeFilters.clear(),this.searchBarService.selectedType=[],this.searchBarService.selectedProductLine=[],this.searchBarService.isSelfProject=!1,this.projectService.setProjectFilterCriteria({})}ngOnDestroy(){this._destroy$.next(),this._destroy$.complete()}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(W.N),e.Y36(H.Y),e.Y36(e.sBO))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-project-filter"]],decls:2,vars:1,consts:[[1,"side-nav-container",3,"click"],["class","filter-section",4,"ngFor","ngForOf"],[1,"filter-section"],["class","fields",4,"ngIf"],["class","fields toggle-container",4,"ngIf"],[1,"fields"],[1,"filter-form-field",3,"click"],["multiple","",3,"formControl","placeholder","selectionChange","click"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],[1,"fields","toggle-container"],[3,"formControl","change"],["mat-stroked-button","",1,"clear-filter-btn",3,"click"]],template:function(i,o){1&i&&(e.TgZ(0,"main",0),e.NdJ("click",function(a){return a.stopPropagation()}),e.YNc(1,_t,3,2,"div",1),e.qZA()),2&i&&(e.xp6(1),e.Q6J("ngForOf",o.filters))},dependencies:[v.sg,v.O5,J.lW,D.KE,D.hX,U.ey,X.gD,ht.Rr,m.JJ,m.oH,S.X$,gt.$],styles:[".side-nav-container[_ngcontent-%COMP%]{padding:.5rem 1rem;display:flex;flex-wrap:wrap;gap:.4rem 1rem;min-width:500px}.filter-section[_ngcontent-%COMP%]{flex:0 1 calc(50% - .5rem)}.filter-section[_ngcontent-%COMP%]   .filter-form-field[_ngcontent-%COMP%]{width:100%}.filter-section[_ngcontent-%COMP%]:nth-child(3){flex:0 1 100%}.clear-filter-btn[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content;font-weight:500}.toggle-container[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}"]})}return n})();var ce=l(905),ye=l(4300),Y=l(2596),j=l(617),N=l(7988),Ct=l(9014),vt=l(3997),xt=l(2181),le=l(2032);let Pe=(()=>{class n{get searchBarBg(){return this.backgroundColor}constructor(t,i){this.searchBarService=t,this.router=i,this.searchChanged=new e.vpe,this.placeholder="Search by name, description, type, product line, or owner...",this.backgroundColor="#ece7e7",this.searchControl=new m.NI,this.routerSubscription=null}ngOnInit(){this.searchControl.valueChanges.pipe((0,se.b)(600),(0,vt.x)()).subscribe(t=>{this.searchChanged.emit(t),this.searchBarService.search(t||"")}),this.routerSubscription=this.router.events.pipe((0,xt.h)(t=>t instanceof M.m2)).subscribe(t=>{("/dashboard"===t.url||"/"===t.url)&&this.resetSearch()})}resetSearch(){this.searchControl.value&&(this.searchControl.setValue(""),this.searchBarService.resetSearch())}ngOnDestroy(){this.routerSubscription&&this.routerSubscription.unsubscribe()}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(W.N),e.Y36(M.F0))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-search-bar"]],hostVars:2,hostBindings:function(i,o){2&i&&e.Udp("--search-bar-bg",o.searchBarBg)},inputs:{placeholder:"placeholder",backgroundColor:"backgroundColor"},outputs:{searchChanged:"searchChanged"},decls:5,vars:2,consts:[[1,"search-input"],["type","text","matInput","",3,"formControl","placeholder"],["searchInput",""],["matSuffix",""]],template:function(i,o){1&i&&(e.TgZ(0,"mat-form-field",0),e._UZ(1,"input",1,2),e.TgZ(3,"mat-icon",3),e._uU(4,"search"),e.qZA()()),2&i&&(e.xp6(1),e.s9C("placeholder",o.placeholder),e.Q6J("formControl",o.searchControl))},dependencies:[D.KE,D.R9,le.Nt,j.Hw,m.Fj,m.JJ,m.oH],styles:[".search-input[_ngcontent-%COMP%]{height:3.7rem;width:100%;min-width:300px;display:flex;align-items:center;justify-content:space-between;font-size:14px}.search-input[_ngcontent-%COMP%]     .mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--search-bar-bg)}.search-input[_ngcontent-%COMP%]     .mat-mdc-form-field{font-size:13px}"]})}return n})(),we=(()=>{class n{transform(t,i){return t?"number"==typeof t||t.length<=i?t:t.substring(0,i)+"...":""}static#e=this.\u0275fac=function(i){return new(i||n)};static#t=this.\u0275pipe=e.Yjl({name:"truncate",type:n,pure:!0})}return n})();var bt=l(2884);const yt=["actionMenuTrigger"];function Pt(n,s){1&n&&(e.TgZ(0,"div",23)(1,"span",24),e._uU(2),e.ALo(3,"translate"),e.qZA()()),2&n&&(e.xp6(2),e.Oqu(e.lcZ(3,1,"dashboard.loading")))}function wt(n,s){if(1&n&&(e.TgZ(0,"th",29),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&n){const t=e.oxw(2).$implicit;e.Q6J("ngClass",t.key),e.xp6(1),e.hij(" ",e.lcZ(2,2,t.display)," ")}}function kt(n,s){1&n&&(e.ynx(0),e.YNc(1,wt,3,4,"th",28),e.BQk())}function Tt(n,s){if(1&n&&(e.TgZ(0,"th",31),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&n){const t=e.oxw(2).$implicit;e.Q6J("ngClass",t.key),e.xp6(1),e.hij(" ",e.lcZ(2,2,t.display)," ")}}function At(n,s){1&n&&e.YNc(0,Tt,3,4,"th",30)}function St(n,s){if(1&n&&(e.TgZ(0,"td",34),e._uU(1),e.ALo(2,"timeAgo"),e.qZA()),2&n){const t=s.$implicit,i=e.oxw(4).$implicit;e.xp6(1),e.hij(" ",e.lcZ(2,1,t[i.key])," ")}}function Mt(n,s){1&n&&(e.ynx(0),e.YNc(1,St,3,3,"td",33),e.BQk())}function Dt(n,s){if(1&n&&(e.TgZ(0,"td",34),e._uU(1),e.ALo(2,"truncate"),e.qZA()),2&n){const t=s.$implicit,i=e.oxw(4).$implicit;e.xp6(1),e.hij(" ",e.xi3(2,1,t[i.key],120)," ")}}function Ot(n,s){1&n&&e.YNc(0,Dt,3,4,"td",33)}function Et(n,s){if(1&n&&(e.ynx(0),e.YNc(1,Mt,2,0,"ng-container",26),e.YNc(2,Ot,1,0,"ng-template",null,32,e.W1O),e.BQk()),2&n){const t=e.MAs(3),i=e.oxw(2).$implicit;e.xp6(1),e.Q6J("ngIf",i.isDate)("ngIfElse",t)}}const It=function(){return["fas","lock"]};function jt(n,s){1&n&&e._UZ(0,"fa-icon",36),2&n&&e.Q6J("icon",e.DdM(1,It))}function Nt(n,s){if(1&n&&(e.TgZ(0,"td",34),e.YNc(1,jt,1,2,"fa-icon",35),e.qZA()),2&n){const t=s.$implicit,i=e.oxw(4);e.xp6(1),e.Q6J("ngIf",i.checkProjectLock(t))}}function Zt(n,s){1&n&&(e.ynx(0),e.YNc(1,Nt,2,1,"td",33),e.BQk())}function Vt(n,s){if(1&n&&(e.ynx(0),e.YNc(1,Et,4,2,"ng-container",22),e.YNc(2,Zt,2,0,"ng-container",22),e.BQk()),2&n){const t=e.oxw().$implicit;e.xp6(1),e.Q6J("ngIf",!t.isLock),e.xp6(1),e.Q6J("ngIf",t.isLock)}}function Ft(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"td",34)(1,"div",37)(2,"button",38)(3,"mat-icon"),e._uU(4,"more_vert"),e.qZA()(),e.TgZ(5,"button",39),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(3);return e.KtG(a.openEditor(r))}),e.ALo(6,"translate"),e.TgZ(7,"mat-icon"),e._uU(8,"launch"),e.qZA()(),e.TgZ(9,"mat-menu",null,40)(11,"button",21),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(3);return e.KtG(a.onEditProject(r))}),e.TgZ(12,"mat-icon"),e._uU(13,"edit"),e.qZA(),e.TgZ(14,"span"),e._uU(15),e.ALo(16,"translate"),e.qZA()(),e.TgZ(17,"button",21),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(3);return e.KtG(a.onShareProject(r))}),e.TgZ(18,"mat-icon"),e._uU(19,"share"),e.qZA(),e.TgZ(20,"span"),e._uU(21),e.ALo(22,"translate"),e.qZA()(),e.TgZ(23,"button",41),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(3);return e.KtG(a.onDeleteProject(r))}),e.TgZ(24,"mat-icon",42),e._uU(25,"delete"),e.qZA(),e.TgZ(26,"span"),e._uU(27),e.ALo(28,"translate"),e.qZA()()()()()}if(2&n){const t=s.$implicit,i=e.MAs(10),o=e.oxw(3);e.xp6(2),e.Q6J("matMenuTriggerFor",i)("disabled",!(0==t.accessType&&!o.checkProjectLock(t))),e.xp6(3),e.Q6J("matTooltip",e.lcZ(6,6,"dashboard.table.open")),e.xp6(10),e.Oqu(e.lcZ(16,8,"dashboard.table.edit")),e.xp6(6),e.Oqu(e.lcZ(22,10,"dashboard.table.share")),e.xp6(6),e.Oqu(e.lcZ(28,12,"dashboard.table.delete"))}}function Lt(n,s){1&n&&(e.ynx(0),e.YNc(1,Ft,29,14,"td",33),e.BQk())}function Rt(n,s){if(1&n&&(e.ynx(0,25),e.YNc(1,kt,2,0,"ng-container",26),e.YNc(2,At,1,0,"ng-template",null,27,e.W1O),e.YNc(4,Vt,3,2,"ng-container",22),e.YNc(5,Lt,2,0,"ng-container",22),e.BQk()),2&n){const t=s.$implicit,i=e.MAs(3);e.Q6J("matColumnDef",t.key),e.xp6(1),e.Q6J("ngIf",!t.isLock&&!t.isAction)("ngIfElse",i),e.xp6(3),e.Q6J("ngIf",!t.isAction),e.xp6(1),e.Q6J("ngIf",t.isAction)}}function Bt(n,s){1&n&&e._UZ(0,"tr",43)}const Ht=function(n){return{gray:n}};function Jt(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"tr",44),e.NdJ("dblclick",function(){const r=e.CHM(t).$implicit,a=e.oxw();return e.KtG(a.openEditor(r))})("contextmenu",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onRightClick(o,a))}),e.qZA()}2&n&&e.Q6J("ngClass",e.VKq(1,Ht,s.even))}function Ut(n,s){if(1&n&&(e.TgZ(0,"tr",45)(1,"td",46),e._uU(2),e.ALo(3,"translate"),e.qZA()()),2&n){const t=e.oxw();e.xp6(2),e.hij(" ",t.isTableLoading?"":e.lcZ(3,1,"dashboard.noProject")," ")}}function Yt(n,s){if(1&n){const t=e.EpF();e.ynx(0),e.TgZ(1,"button",21),e.NdJ("click",function(){e.CHM(t);const o=e.oxw();return e.KtG(o.onEditProject(o.contextMenuRow))}),e.TgZ(2,"mat-icon"),e._uU(3,"edit"),e.qZA(),e.TgZ(4,"span"),e._uU(5),e.ALo(6,"translate"),e.qZA()(),e.TgZ(7,"button",21),e.NdJ("click",function(){e.CHM(t);const o=e.oxw();return e.KtG(o.onShareProject(o.contextMenuRow))}),e.TgZ(8,"mat-icon"),e._uU(9,"share"),e.qZA(),e.TgZ(10,"span"),e._uU(11),e.ALo(12,"translate"),e.qZA()(),e.TgZ(13,"button",41),e.NdJ("click",function(){e.CHM(t);const o=e.oxw();return e.KtG(o.onDeleteProject(o.contextMenuRow))}),e.TgZ(14,"mat-icon",42),e._uU(15,"delete"),e.qZA(),e.TgZ(16,"span"),e._uU(17),e.ALo(18,"translate"),e.qZA()(),e.BQk()}2&n&&(e.xp6(5),e.Oqu(e.lcZ(6,3,"dashboard.table.edit")),e.xp6(6),e.Oqu(e.lcZ(12,5,"dashboard.table.share")),e.xp6(6),e.Oqu(e.lcZ(18,7,"dashboard.table.delete")))}const Gt=function(){return["fal","filter"]},Kt=function(){return[10,15,20]};let ke=(()=>{class n{set loading(t){this.isTableLoading!==t&&setTimeout(()=>{this.isTableLoading=t,this.cdr.markForCheck()})}constructor(t,i,o,r){this.searchBarService=t,this.userService=i,this.liveAnnouncer=o,this.cdr=r,this.noOfFilter=0,this.currentPage=0,this.isTableLoading=!1,this.columnConfig=[{key:"isLock",display:"",isAction:!1,isLock:!0},{key:"name",display:"dashboard.table.name",isAction:!1},{key:"description",display:"dashboard.table.description",isAction:!1},{key:"type",display:"dashboard.table.type",isAction:!1},{key:"productLine",display:"dashboard.table.productLine",isAction:!1},{key:"admin",display:"dashboard.table.owner",isAction:!1},{key:"lastModifiedDate",display:"dashboard.table.lastModified",isAction:!1,isDate:!0},{key:"action",display:"dashboard.table.action",isAction:!0}],this.displayedColumns=this.columnConfig.map(a=>a.key),this.menuPosition={x:"0px",y:"0px"},this.openProject=new e.vpe,this.editProject=new e.vpe,this.deleteProject=new e.vpe,this.shareProject=new e.vpe,this.paginationChanged=new e.vpe,this.sortChange=new e.vpe,this.sortConfig={active:"lastModifiedDate",direction:"desc"},this.dataSource=new w.by,this.totalCount=0,this.pageSize=10,(0,e.cEC)(()=>{this.noOfFilter=this.searchBarService.filterCount()})}ngAfterViewInit(){this.sort&&(this.sort.active=this.sortConfig.active,this.sort.direction=this.sortConfig.direction)}onRightClick(t,i){t.preventDefault(),this.contextMenuRow=i,this.menuPosition={x:`${t.clientX}px`,y:t.clientY-40+"px"},setTimeout(()=>{this.actionMenuTrigger.openMenu()})}announceSortChange(t){this.liveAnnouncer.announce(t.direction?`Sorted ${t.direction}ending`:"Sorting cleared"),this.sortChange.emit(t)}openEditor(t){this.projectFilterComponent&&this.projectFilterComponent.resetFilters(),this.openProject.emit(t)}onEditProject(t){this.editProject.emit(t)}onDeleteProject(t){this.deleteProject.emit(t)}onShareProject(t){this.shareProject.emit(t)}checkProjectLock(t){const i=this.userService.getUser();return!(!i||!t.isProjectLocked||0===t.lockingIdContact||t.lockingIdContact===i.id)}applyFilter(t){null!==t&&this.searchBarService.search(t.trim())}clearFilter(){this.searchBarService.search("")}onPageChange(t){this.currentPage=t.pageIndex+1,this.pageSize=t.pageSize,this.paginationChanged.emit({pageSize:this.pageSize,currentPage:this.currentPage})}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(W.N),e.Y36(ce.K),e.Y36(ye.Kd),e.Y36(e.sBO))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-project-table"]],viewQuery:function(i,o){if(1&i&&(e.Gf(ae.NW,5),e.Gf(q.YE,5),e.Gf(yt,7),e.Gf(be,5)),2&i){let r;e.iGM(r=e.CRH())&&(o.paginator=r.first),e.iGM(r=e.CRH())&&(o.sort=r.first),e.iGM(r=e.CRH())&&(o.actionMenuTrigger=r.first),e.iGM(r=e.CRH())&&(o.projectFilterComponent=r.first)}},inputs:{sortConfig:"sortConfig",dataSource:"dataSource",totalCount:"totalCount",pageSize:"pageSize",loading:["isLoading","loading"]},outputs:{openProject:"open",editProject:"edit",deleteProject:"delete",shareProject:"share",paginationChanged:"paginationChanged",sortChange:"sortChange"},decls:34,vars:39,consts:[[1,"table-container"],[1,"table-header"],[1,"header-left",3,"matMenuTriggerFor"],["size","xl","matTooltipClass","tooltip-custom",1,"header-icon",3,"icon","matTooltip"],["matBadgeOverlap","false","matBadgeSize","small",1,"header-paragraph",3,"matBadge","matBadgeHidden"],[3,"overlapTrigger","click"],["filterMenu","matMenu"],[1,"search-container",3,"placeholder","searchChanged"],[1,"mat-elevation","table"],["class","table-loading-shade",4,"ngIf"],["mat-table","","matSort","",3,"dataSource","matSortActive","matSortDirection","matSortChange"],[3,"matColumnDef",4,"ngFor","ngForOf"],["mat-header-row","","class","table-header-row",4,"matHeaderRowDef","matHeaderRowDefSticky"],["mat-row","","class","example-element-row",3,"ngClass","dblclick","contextmenu",4,"matRowDef","matRowDefColumns"],["class","mat-row",4,"matNoDataRow"],[1,"table-bottom"],["showFirstLastButtons","","aria-label","Select page of periodic elements",3,"pageSize","length","pageSizeOptions","disabled","page"],[1,"hidden-menu-trigger"],["mat-icon-button","",2,"visibility","hidden",3,"matMenuTriggerFor"],["actionMenuTrigger","matMenuTrigger"],["actionMenu","matMenu"],["mat-menu-item","",3,"click"],[4,"ngIf"],[1,"table-loading-shade"],[1,"loading-text"],[3,"matColumnDef"],[4,"ngIf","ngIfElse"],["unsortableHeader",""],["mat-header-cell","","mat-sort-header","",3,"ngClass",4,"matHeaderCellDef"],["mat-header-cell","","mat-sort-header","",3,"ngClass"],["mat-header-cell","",3,"ngClass",4,"matHeaderCellDef"],["mat-header-cell","",3,"ngClass"],["regularCell",""],["mat-cell","",4,"matCellDef"],["mat-cell",""],["size","lg","class","lock-icon",3,"icon",4,"ngIf"],["size","lg",1,"lock-icon",3,"icon"],[1,"action-column"],["mat-icon-button","","aria-label","More actions",1,"action-btn",3,"matMenuTriggerFor","disabled"],["mat-icon-button","","aria-label","Open Project",1,"action-btn",3,"matTooltip","click"],["menu","matMenu"],["mat-menu-item","",1,"delete-menu",3,"click"],[1,"menu-icon"],["mat-header-row","",1,"table-header-row"],["mat-row","",1,"example-element-row",3,"ngClass","dblclick","contextmenu"],[1,"mat-row"],["colspan","8",1,"mat-cell","no-project-text"]],template:function(i,o){if(1&i&&(e.TgZ(0,"div",0)(1,"div",1)(2,"div",2),e._UZ(3,"fa-icon",3),e.ALo(4,"translate"),e.TgZ(5,"p",4),e._uU(6),e.ALo(7,"translate"),e.qZA()(),e.TgZ(8,"mat-menu",5,6),e.NdJ("click",function(a){return a.stopPropagation()}),e._UZ(10,"app-project-filter"),e.qZA(),e.TgZ(11,"app-search-bar",7),e.NdJ("searchChanged",function(a){return o.applyFilter(a)}),e.ALo(12,"translate"),e.qZA()(),e.TgZ(13,"div",8),e.YNc(14,Pt,4,3,"div",9),e.TgZ(15,"table",10),e.NdJ("matSortChange",function(a){return o.announceSortChange(a)}),e.YNc(16,Rt,6,5,"ng-container",11),e.YNc(17,Bt,1,0,"tr",12),e.YNc(18,Jt,1,3,"tr",13),e.YNc(19,Ut,4,3,"tr",14),e.qZA()(),e.TgZ(20,"div",15)(21,"mat-paginator",16),e.NdJ("page",function(a){return o.onPageChange(a)}),e.qZA()()(),e.TgZ(22,"div",17),e._UZ(23,"button",18,19),e.qZA(),e.TgZ(25,"mat-menu",null,20)(27,"button",21),e.NdJ("click",function(){return o.openEditor(o.contextMenuRow)}),e.TgZ(28,"mat-icon"),e._uU(29,"launch"),e.qZA(),e.TgZ(30,"span"),e._uU(31),e.ALo(32,"translate"),e.qZA()(),e.YNc(33,Yt,19,9,"ng-container",22),e.qZA()),2&i){const r=e.MAs(9),a=e.MAs(26);e.xp6(2),e.Q6J("matMenuTriggerFor",r),e.xp6(1),e.Q6J("icon",e.DdM(37,Gt))("matTooltip",e.lcZ(4,29,"dashboard.table.filter")),e.xp6(2),e.Q6J("matBadge",o.noOfFilter)("matBadgeHidden",0==o.noOfFilter),e.xp6(1),e.hij(" ",e.lcZ(7,31,"dashboard.table.filter")," "),e.xp6(2),e.Q6J("overlapTrigger",!1),e.xp6(3),e.s9C("placeholder",e.lcZ(12,33,"dashboard.search.placeholder")),e.xp6(3),e.Q6J("ngIf",o.isTableLoading),e.xp6(1),e.ekj("loading-results",o.isTableLoading),e.Q6J("dataSource",o.dataSource)("matSortActive",o.sortConfig.active)("matSortDirection",o.sortConfig.direction),e.xp6(1),e.Q6J("ngForOf",o.columnConfig),e.xp6(1),e.Q6J("matHeaderRowDef",o.displayedColumns)("matHeaderRowDefSticky",!0),e.xp6(1),e.Q6J("matRowDefColumns",o.displayedColumns),e.xp6(3),e.Q6J("pageSize",o.pageSize)("length",o.totalCount)("pageSizeOptions",e.DdM(38,Kt))("disabled",o.totalCount<=10),e.xp6(1),e.Udp("left",o.menuPosition.x)("top",o.menuPosition.y),e.xp6(1),e.Q6J("matMenuTriggerFor",a),e.xp6(8),e.Oqu(e.lcZ(32,35,"dashboard.table.open")),e.xp6(2),e.Q6J("ngIf",o.contextMenuRow&&0==o.contextMenuRow.accessType&&!o.checkProjectLock(o.contextMenuRow))}},dependencies:[v.mk,v.sg,v.O5,J.RK,Y.gM,j.Hw,N.VK,N.OP,N.p6,w.BZ,w.fO,w.as,w.w1,w.Dz,w.nj,w.ge,w.ev,w.XQ,w.Gk,w.Ee,q.YE,q.nU,ae.NW,Ct.k,R.BN,Pe,be,S.X$,we,bt.e],styles:[".table-container[_ngcontent-%COMP%]{height:calc(100vh - 3.875rem);border:1px solid rgb(209,199,199);display:flex;flex-direction:column}.table-header[_ngcontent-%COMP%]{display:flex;height:2.55rem;justify-content:space-between;padding-inline:.5rem;padding-top:.2rem;border-bottom:1px solid rgb(209,199,199)}.table-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:15px}.table-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{cursor:pointer;font-size:small;margin-right:.5rem}.table-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-paragraph[_ngcontent-%COMP%]{cursor:pointer}.table-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{min-width:250px}.hidden-menu-trigger[_ngcontent-%COMP%]{position:fixed;z-index:1000}.action-column[_ngcontent-%COMP%]{display:flex;margin-right:.2rem}.mat-mdc-table[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%]{font-size:15px;font-weight:500}.mat-elevation.table[_ngcontent-%COMP%]{flex-grow:1;overflow:auto;position:relative}.mat-elevation.table[_ngcontent-%COMP%]   .table-loading-shade[_ngcontent-%COMP%]{position:absolute;inset:0;background:rgba(255,255,255,.8);z-index:10;display:flex;flex-direction:column;align-items:center;justify-content:center}.mat-elevation.table[_ngcontent-%COMP%]   .table-loading-shade[_ngcontent-%COMP%]   .loading-text[_ngcontent-%COMP%]{margin-top:3rem;font-size:1rem;color:#000000b3}.mat-elevation.table[_ngcontent-%COMP%]   table.loading-results[_ngcontent-%COMP%]{opacity:.6}.table-bottom[_ngcontent-%COMP%]{margin-top:5px}.mat-mdc-row[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%]{cursor:pointer;padding-block:.25rem}.mat-mdc-row[_ngcontent-%COMP%]:hover{background:whitesmoke}.no-project-text[_ngcontent-%COMP%]{padding:2rem}.lock-icon[_ngcontent-%COMP%]{color:red}.isLock[_ngcontent-%COMP%]{width:1%}.name[_ngcontent-%COMP%]{width:18%}.description[_ngcontent-%COMP%]{width:29%}.type[_ngcontent-%COMP%], .productLine[_ngcontent-%COMP%]{width:12%}.owner[_ngcontent-%COMP%]{width:16%}.delete-menu[_ngcontent-%COMP%]{color:red}.delete-menu[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]{color:inherit}.gray[_ngcontent-%COMP%]{background:#fbfbfb}"],changeDetection:0})}return n})();var de=l(6028),k=l(7700),Te=l(7921),pe=l(420);let Ae=(()=>{class n{constructor(t,i){this.projectPermissionApi=t,this.snackBarService=i}giveProjectPermission(t){this.projectPermissionApi.givePermission(t).subscribe(()=>{this.snackBarService.openSnackbar("snackBar.projectShareMsg")})}getProjectPermissions(t){return this.projectPermissionApi.getProjectPermissions(t)}checkPermission(t){return this.projectPermissionApi.checkPermission(t)}removePermission(t){this.projectPermissionApi.removePermission(t).subscribe(()=>{this.snackBarService.openSnackbar("snackBar.permissionRemovedMsg")})}updatePermission(t){this.projectPermissionApi.updatePermission(t).subscribe(()=>{this.snackBarService.openSnackbar("snackBar.accessChangedMsg")})}static#e=this.\u0275fac=function(i){return new(i||n)(e.LFG(ve),e.LFG(pe.c))};static#t=this.\u0275prov=e.Yz7({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Se=l(7157),$=l(2557),Me=l(4630),qt=l(6385);function zt(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"mat-chip-row",21),e.NdJ("removed",function(){const r=e.CHM(t).$implicit,a=e.oxw();return e.KtG(a.removeChip(r))}),e._uU(1),e.TgZ(2,"button",22)(3,"mat-icon"),e._uU(4,"cancel"),e.qZA()()()}if(2&n){const t=s.$implicit;e.Q6J("editable",!0)("aria-description","press enter to edit "+t),e.xp6(1),e.hij(" ",t," "),e.xp6(1),e.uIk("aria-label","remove "+t)}}function Qt(n,s){if(1&n&&(e.TgZ(0,"mat-option",23),e._uU(1),e.qZA()),2&n){const t=s.$implicit;e.Q6J("value",t),e.xp6(1),e.hij(" ",t," ")}}function Wt(n,s){if(1&n&&(e.ynx(0),e.TgZ(1,"mat-option",23),e._uU(2),e.ALo(3,"translate"),e.qZA(),e.BQk()),2&n){const t=s.$implicit;e.xp6(1),e.Q6J("value",t.value),e.xp6(1),e.hij(" ",e.lcZ(3,2,"shareDialog."+t.value)," ")}}function Xt(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"mat-form-field",24)(1,"mat-label"),e._uU(2),e.ALo(3,"translate"),e.qZA(),e.TgZ(4,"mat-select",25),e.NdJ("valueChange",function(o){e.CHM(t);const r=e.oxw();return e.KtG(r.shareProjectForm.value.access=o)}),e.YNc(5,Wt,4,4,"ng-container",26),e.qZA()()}if(2&n){const t=e.oxw();e.xp6(2),e.Oqu(e.lcZ(3,3,"shareDialog.access")),e.xp6(2),e.Q6J("value",t.shareProjectForm.value.access),e.xp6(1),e.Q6J("ngForOf",t.accessTypes)}}function $t(n,s){1&n&&(e.TgZ(0,"p",27),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&n&&(e.xp6(1),e.hij(" ",e.lcZ(2,1,"shareDialog.accessTitle")," "))}function ei(n,s){if(1&n){const t=e.EpF();e.ynx(0),e.TgZ(1,"button",37),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw().$implicit,c=e.oxw(2);return e.KtG(c.changeAccess(a.id,r.value))}),e._uU(2),e.ALo(3,"translate"),e.qZA(),e.BQk()}if(2&n){const t=s.$implicit;e.xp6(2),e.hij(" ",e.lcZ(3,1,"shareDialog."+t.value)," ")}}function ti(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"button",37),e.NdJ("click",function(){e.CHM(t);const o=e.oxw().$implicit,r=e.oxw(2);return e.KtG(r.removePermission(o.id))}),e._uU(1),e.ALo(2,"translate"),e.qZA()}2&n&&(e.xp6(1),e.hij(" ",e.lcZ(2,1,"shareDialog.removeAccess")," "))}function ii(n,s){if(1&n&&(e.TgZ(0,"div",30)(1,"div",31)(2,"p",32),e._uU(3),e.qZA()(),e.TgZ(4,"div",33)(5,"button",34)(6,"span"),e._uU(7),e.ALo(8,"translate"),e.qZA(),e.TgZ(9,"mat-icon"),e._uU(10,"arrow_drop_down"),e.qZA()(),e.TgZ(11,"mat-menu",null,35),e.YNc(13,ei,4,3,"ng-container",26),e.YNc(14,ti,3,3,"button",36),e.qZA()()()),2&n){const t=s.$implicit,i=e.MAs(12),o=e.oxw(2);e.xp6(3),e.Oqu(t.email),e.xp6(2),e.Q6J("matMenuTriggerFor",i)("disabled",o.currentUser.email==t.email),e.xp6(2),e.Oqu(e.lcZ(8,6,"shareDialog."+t.accessType)),e.xp6(6),e.Q6J("ngForOf",o.accessTypes),e.xp6(1),e.Q6J("ngIf",o.isProjectOwner)}}function oi(n,s){if(1&n&&(e.TgZ(0,"div",28),e.YNc(1,ii,15,8,"div",29),e.qZA()),2&n){const t=e.oxw();e.xp6(1),e.Q6J("ngForOf",t.accessList)}}function ni(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"button",38),e.NdJ("click",function(){e.CHM(t);const o=e.oxw();return e.KtG(o.savePermission())}),e.ALo(1,"translate"),e._uU(2),e.ALo(3,"translate"),e.qZA()}2&n&&(e.Q6J("matTooltip",e.lcZ(1,2,"window.save")),e.xp6(2),e.hij(" ",e.lcZ(3,4,"window.save")," "))}function ri(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"button",39),e.NdJ("click",function(){e.CHM(t);const o=e.oxw();return e.KtG(o.onSubmit())}),e._uU(1),e.ALo(2,"translate"),e.qZA()}2&n&&(e.xp6(1),e.hij(" ",e.lcZ(2,1,"shareDialog.send")," "))}const ai=function(){return["fal","xmark"]},si=function(n){return{copied:n}};let ci=(()=>{class n{constructor(t,i,o,r,a,c){this.dialogRef=t,this.formBuilder=i,this.permissionService=o,this.userService=r,this.sharedService=a,this.shareData=c,this.emailControl=new m.NI("",[m.kI.required,m.kI.email]),this.shareProjectForm=this.formBuilder.group({email:this.emailControl,access:[x.WP.Viewer,m.kI.required]}),this.accessList=[],this.dropdownOpen=!1,this.isValidEmail=!1,this.isProjectOwner=!1,this.projectId=0,this.emails=[],this.addOnBlur=!0,this.allEmails=[],this._isAccessChanged=!1,this.isCopied=!1,this.separatorKeysCodes=[de.K5,de.OC,de.L_],this.accessTypes=Object.values(x.WP).filter(d=>"number"==typeof d).map(d=>({label:x.WP[d],value:d}))}ngOnInit(){this.projectId=this.shareData.projectId,this.permissionService.getProjectPermissions(this.projectId).subscribe(t=>{this.accessList=t}),this.shareData.accessType==x.WP.Admin&&(this.isProjectOwner=!0),this.filteredEmails=this.emailControl.valueChanges.pipe((0,Te.O)(""),(0,re.U)(t=>this._filter(t||""))),this.userService.userEmailsChanges().subscribe(t=>{t.length>0&&(this.currentUser=this.userService.getUser(),this.currentUser&&(this.allEmails=t.filter(i=>i!==this.currentUser.email)))})}_filter(t){const i=t.toLowerCase(),o=this.accessList.map(r=>r.email);return this.allEmails.filter(r=>r.toLowerCase().includes(i)&&!this.emails.includes(r)&&!o.includes(r))}addChipFromAutocomplete(t){const i=t.option.viewValue;i&&!this.emails.includes(i)&&(this.emails.push(i),this.filterEmails())}changeAccess(t,i){const o=this.accessList.find(r=>r.id===t);o&&(o.accessType=i,this._isAccessChanged=!0)}closeDialog(){this.dialogRef.close()}clearInput(){this.emailControl.setValue(""),this.emailControl.markAsUntouched(),this.emailControl.updateValueAndValidity()}onSubmit(){if(this.shareProjectForm.valid&&this.projectId){const t={idProject:this.projectId,admins:[],viewers:[],editors:[]};if(null!=this.shareProjectForm.value.access){const i=+this.shareProjectForm.value.access;i===x.WP.Viewer?t.viewers=this.emails:i===x.WP.Editor?t.editors=this.emails:i===x.WP.Admin&&(t.admins=this.emails)}this.dialogRef.close({isShared:!0,permission:t})}}copyLink(){const t=window.location.origin+`/editor/${this.projectId}/diagram/0`;this.sharedService.copyToClipboard(t,"snackBar.linkCopiedMsg",""),this.isCopied=!0,setTimeout(()=>{this.isCopied=!1},3e3)}addChip(t){const i=(t.value||"").trim();if(i&&this.emailControl.valid&&!this.emails.includes(i)){if(this.accessList.find(o=>o.email===i))return void t.chipInput.clear();this.emails.push(i),this.filterEmails()}t.chipInput.clear()}removeChip(t){const i=this.emails.indexOf(t);i>=0&&(this.emails.splice(i,1),this.filterEmails())}filterEmails(){this.filteredEmails=this.emailControl.valueChanges.pipe((0,Te.O)(""),(0,re.U)(t=>this._filter(t||"")))}removePermission(t){this.permissionService.removePermission(t),this.accessList=this.accessList.filter(i=>i.id!==t),this._isAccessChanged=!0}savePermission(){this._isAccessChanged?this.dialogRef.close({isShared:!1,accesses:this.accessList}):this.dialogRef.close()}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(k.so),e.Y36(m.qu),e.Y36(Ae),e.Y36(ce.K),e.Y36(Se.F),e.Y36(k.WI))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-share-dialog"]],decls:34,vars:35,consts:[[1,"dialog-header"],["mat-dialog-title","",1,"dialog-title"],["mat-ripple","","size","xl",1,"close-button",3,"icon","click"],[3,"formGroup"],[1,"share-dialog"],[1,"add-people"],["appearance","outline",1,"share-field"],["aria-label","Enter Email"],["chipGrid",""],["class","email-chip",3,"editable","aria-description","removed",4,"ngFor","ngForOf"],["formControlName","email",3,"placeholder","matChipInputFor","matChipInputSeparatorKeyCodes","matChipInputAddOnBlur","matAutocomplete","matChipInputTokenEnd","click"],[3,"optionSelected"],["auto","matAutocomplete"],[3,"value",4,"ngFor","ngForOf"],["class","share-field select-access","appearance","outline",4,"ngIf"],["class","title",4,"ngIf"],["class","access-list",4,"ngIf"],[1,"dialog-actions"],["mat-flat-button","","cdkFocusInitial","","color","primary","class","dialog-btn btn","type","submit",3,"matTooltip","click",4,"ngIf"],["mat-flat-button","","cdkFocusInitial","","color","primary","class","dialog-btn btn","type","submit",3,"click",4,"ngIf"],["mat-flat-button","",1,"copy-btn",3,"ngClass","matTooltip","click"],[1,"email-chip",3,"editable","aria-description","removed"],["matChipRemove",""],[3,"value"],["appearance","outline",1,"share-field","select-access"],[3,"value","valueChange"],[4,"ngFor","ngForOf"],[1,"title"],[1,"access-list"],["class","access-item",4,"ngFor","ngForOf"],[1,"access-item"],[1,"left"],[1,"email"],[1,"right"],["mat-button","",1,"access-btn",3,"matMenuTriggerFor","disabled"],["optionMenu","matMenu"],["mat-menu-item","",3,"click",4,"ngIf"],["mat-menu-item","",3,"click"],["mat-flat-button","","cdkFocusInitial","","color","primary","type","submit",1,"dialog-btn","btn",3,"matTooltip","click"],["mat-flat-button","","cdkFocusInitial","","color","primary","type","submit",1,"dialog-btn","btn",3,"click"]],template:function(i,o){if(1&i&&(e.TgZ(0,"div",0)(1,"h2",1),e._uU(2),e.ALo(3,"translate"),e.qZA(),e.TgZ(4,"fa-icon",2),e.NdJ("click",function(){return o.closeDialog()}),e.qZA()(),e.TgZ(5,"form",3)(6,"mat-dialog-content",4)(7,"div",5)(8,"mat-form-field",6)(9,"mat-label"),e._uU(10),e.ALo(11,"translate"),e.qZA(),e.TgZ(12,"mat-chip-grid",7,8),e.YNc(14,zt,5,4,"mat-chip-row",9),e.TgZ(15,"input",10),e.NdJ("matChipInputTokenEnd",function(a){return o.addChip(a)})("click",function(){return o.clearInput()}),e.ALo(16,"translate"),e.qZA(),e.TgZ(17,"mat-autocomplete",11,12),e.NdJ("optionSelected",function(a){return o.addChipFromAutocomplete(a)}),e.YNc(19,Qt,2,2,"mat-option",13),e.ALo(20,"async"),e.qZA()()(),e.YNc(21,Xt,6,5,"mat-form-field",14),e.qZA(),e.YNc(22,$t,3,3,"p",15),e.YNc(23,oi,2,1,"div",16),e._UZ(24,"mat-divider"),e.qZA(),e.TgZ(25,"mat-dialog-actions",17),e.YNc(26,ni,4,6,"button",18),e.YNc(27,ri,3,3,"button",19),e.TgZ(28,"button",20),e.NdJ("click",function(){return o.copyLink()}),e.ALo(29,"translate"),e._uU(30),e.ALo(31,"translate"),e.TgZ(32,"mat-icon"),e._uU(33),e.qZA()()()()),2&i){const r=e.MAs(13),a=e.MAs(18);e.xp6(2),e.hij(" ",e.lcZ(3,20,"shareDialog.title")," "),e.xp6(2),e.Q6J("icon",e.DdM(32,ai)),e.xp6(1),e.Q6J("formGroup",o.shareProjectForm),e.xp6(5),e.Oqu(e.lcZ(11,22,"shareDialog.addPeople")),e.xp6(4),e.Q6J("ngForOf",o.emails),e.xp6(1),e.s9C("placeholder",e.lcZ(16,24,"shareDialog.email")),e.Q6J("matChipInputFor",r)("matChipInputSeparatorKeyCodes",o.separatorKeysCodes)("matChipInputAddOnBlur",o.addOnBlur)("matAutocomplete",a),e.xp6(4),e.Q6J("ngForOf",e.lcZ(20,26,o.filteredEmails)),e.xp6(2),e.Q6J("ngIf",o.emails.length>0),e.xp6(1),e.Q6J("ngIf",0==o.emails.length),e.xp6(1),e.Q6J("ngIf",0==o.emails.length),e.xp6(3),e.Q6J("ngIf",o.accessList.length>1&&0==o.emails.length),e.xp6(1),e.Q6J("ngIf",o.emails.length>0),e.xp6(1),e.Q6J("ngClass",e.VKq(33,si,o.isCopied))("matTooltip",e.lcZ(29,28,"shareDialog.copyBtn")),e.xp6(2),e.hij(" ",e.lcZ(31,30,"shareDialog.copyBtn")," "),e.xp6(3),e.Oqu(o.isCopied?"done":"link_copy")}},dependencies:[v.mk,v.sg,v.O5,J.lW,D.KE,D.hX,Y.gM,j.Hw,$.RA,$.oH,$.qH,$.z3,Me.XC,U.ey,Me.ZL,N.VK,N.OP,N.p6,qt.d,k.uh,k.xY,k.H8,X.gD,U.wG,m._Y,m.Fj,m.JJ,m.JL,R.BN,m.sg,m.u,v.Ov,S.X$],styles:[".dialog-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding-inline:1rem;background:var(--primary-color);height:var(--toolbar-height);color:#fff}.dialog-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-start;margin:0;padding:0;color:#fff;font-size:17px}.dialog-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding-inline:1.5rem}.dialog-btn[_ngcontent-%COMP%]{color:#f5f4f4;width:5rem;margin:.2rem}.close-button[_ngcontent-%COMP%]{cursor:pointer}.share-dialog[_ngcontent-%COMP%]{width:500px;color:#000;overflow:hidden}@media (max-width: 462px){.share-dialog[_ngcontent-%COMP%]{width:300px}}.add-people[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.share-field[_ngcontent-%COMP%]{width:100%;margin-right:.2rem}.share-field.select-access[_ngcontent-%COMP%]{width:40%;height:-moz-fit-content;height:fit-content}.access-list[_ngcontent-%COMP%]{max-height:220px;overflow:auto;scroll-padding-top:1rem;padding-left:.2rem}.access-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:3rem;border-bottom:.1px solid #f3ecec}.access-item[_ngcontent-%COMP%]:hover{background-color:#f5f5f5;cursor:pointer}.access-btn[_ngcontent-%COMP%]{flex-direction:row-reverse;justify-content:center;align-items:center;margin-right:0;padding:.5rem}.title[_ngcontent-%COMP%]{margin:0;font-weight:700;padding:.4rem;border-bottom:1px solid #ccc}.left[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-start}.right[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end}.avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;margin-right:10px}.email[_ngcontent-%COMP%]{color:#666}.copy-btn[_ngcontent-%COMP%]{border:1px solid var(--primary-color);padding:.5rem}.copy-btn.copied[_ngcontent-%COMP%]{background-color:#28a745;color:#fff}[_nghost-%COMP%]     .mdc-evolution-chip-set__chips{max-width:100%}[_nghost-%COMP%]     .mat-mdc-standard-chip .mdc-evolution-chip__cell--primary, [_nghost-%COMP%]     .mat-mdc-standard-chip .mdc-evolution-chip__action--primary, [_nghost-%COMP%]     .mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:hidden}[_nghost-%COMP%]     .mdc-dialog .mdc-dialog__content{padding:20px}"]})}return n})();var li=l(1723),Z=l(9241),ee=l(862);let di=(()=>{class n{get isLoading(){return this.projectService.isLoading()}get totalProjectCount(){return this.projectService.totalProjectCount()}get totalRecords(){return this.projectService.totalProjectCount()}get pageSize(){return this.projectService.pageSize()}get currentSort(){return this.projectService.currentSort()}constructor(t,i,o,r,a,c,d,g,C,f){this.dialog=t,this.projectService=i,this.snackBarService=o,this.permissionService=r,this.diagramUtils=a,this.userService=c,this.linkService=d,this.searchBarService=g,this.cdr=C,this.liveAnnouncer=f,this.destroy$=new z.x,this.projects=[],this.filteredProjects=[],this.dataSource=new w.by([]),(0,e.cEC)(()=>{this.projects=this.projectService.projects(),this.filteredProjects=this.projectService.filteredProjects(),this.updateDataSource()})}updateDataSource(){this.dataSource=new w.by(this.filteredProjects),this.paginator&&(this.dataSource.paginator=this.paginator),this.sort&&(this.dataSource.sort=this.sort),this.cdr.markForCheck()}ngAfterViewInit(){this.projectTable&&(this.projectTable.sortConfig=this.currentSort),this.userService.retrieveUserEmails(),this.linkService.initLinkTypes(),setTimeout(()=>{this.projectService.refreshProjects().subscribe(),this.setupProjectFiltering(),this.setupFilterCriteriaSubscription()})}setupFilterCriteriaSubscription(){this.projectService.projectFilterCriteriaChanges().pipe((0,I.R)(this.destroy$),(0,xe.T)(1),(0,se.b)(300)).subscribe(t=>{this.projectService.refreshProjects().subscribe()})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}ngOnInit(){this.searchBarService.resetSearch()}setupProjectFiltering(){this.projectService.getProjectSearchTerm().pipe((0,I.R)(this.destroy$),(0,xe.T)(1)).subscribe(()=>{this.projectService.refreshProjects().subscribe()})}refreshUserDataAndLinkTypes(){this.userService.retrieveUserEmails(),this.linkService.initLinkTypes()}initializeTableData(){this.dataSource=new w.by(this.filteredProjects),this.paginator&&(this.dataSource.paginator=this.paginator),this.sort&&(this.dataSource.sort=this.sort)}onSelectProject(t){this.projectService.openProject(t.id,!0),this.diagramUtils.clearEnumTypeOptions()}getAllProjects(t,i,o,r){this.projectService.isLoading.set(!0);const a=this.projectService.getProjectFilterCriteria();return this.projectService.getProjects(i,o,""===this.currentSort.active?"lastModifiedDate":this.currentSort.active,"desc"===this.currentSort.direction,void 0,a,r).pipe((0,lt.x)(()=>{this.cdr.detectChanges()}),(0,E.K)(g=>(console.error("Error fetching projects:",g),this.snackBarService.openSnackbar("dashboard.error.loadingProjects"),(0,dt._)(()=>g)))).subscribe(),(0,pt.H)(300).pipe((0,I.R)(this.destroy$))}openProjectDialog(t){const i=t&&t.accessType!=x.WP.Viewer,o={title:i?"dashboard.editProject":"dashboard.newProject",btnText:i?"dialog.save":"dialog.create",...i&&{project:t}};this.dialog.open(li.Q,{data:o,autoFocus:!1}).afterClosed().subscribe(a=>{if(!a)return;const c=a.isCreation?this.projectService.createNewProject(a.project):this.projectService.updateProject(a.project);this.diagramUtils.clearEnumTypeOptions(),c.subscribe(d=>{if(d&&d.id){if(a.isCreation)this.projectService.openProject(d.id);else if(t){const g={...t,...d};this.updateProjectInList(g)}this.snackBarService.openSnackbar(a.isCreation?"snackBar.projectCreationMsg":"snackBar.projectUpdatedMsg"),a.isCreation&&this.refreshUserDataAndLinkTypes()}})})}updateProjectInList(t){const i=this.projects.findIndex(r=>r.id===t.id),o=this.filteredProjects.findIndex(r=>r.id===t.id);-1!==i&&-1!==o&&(this.projects.splice(i,1),this.filteredProjects.splice(o,1),this.projects.unshift(t),this.filteredProjects.unshift(t),this.projectService.setProjectFilterCriteria(this.projectService.getProjectFilterCriteria()),this.initializeTableData())}deleteProject(t){t.accessType!=x.WP.Viewer&&this.dialog.open(Q.X,{width:"320px",data:{title:"dialog.title",reject:"dialog.no",confirm:"dialog.yes"}}).afterClosed().subscribe(o=>{o&&this.projectService.deleteProject(t.id).subscribe({next:()=>{this.snackBarService.openSnackbar("snackBar.deleteProjectMsg")},error:r=>{console.error("Error deleting project:",r),this.snackBarService.openSnackbar("dashboard.error.deletingProject")}})})}openShareProjectDialog(t){t.accessType!=x.WP.Viewer&&this.dialog.open(ci,{data:{projectId:t.id,accessType:t.accessType},autoFocus:!1}).afterClosed().subscribe(o=>{o&&(o.isShared&&o.permission?(this.permissionService.giveProjectPermission(o.permission),this.refreshUserDataAndLinkTypes()):o.accesses&&(this.permissionService.updatePermission(o.accesses),this.refreshUserDataAndLinkTypes()))})}onPageChange(t){this.projectService.pageSize.set(t.pageSize),this.projectService.currentPage.set(t.currentPage),this.projectService.refreshProjects().subscribe({error:i=>{console.error("Error during page change:",i),this.snackBarService.openSnackbar("dashboard.error.loadingProjects")}})}onSortChange(t){const i={active:t.active,direction:"asc"===t.direction?"asc":"desc"};t.direction?this.liveAnnouncer.announce(`Sorted ${t.direction}ending`):(this.liveAnnouncer.announce("Sorting cleared"),i.active="lastModifiedDate",i.direction="desc"),this.projectService.currentSort.set(i),this.projectService.currentPage.set(1),this.projectService.refreshProjects().subscribe({error:o=>{console.error("Error during sort change:",o),this.snackBarService.openSnackbar("dashboard.error.loadingProjects")}})}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(k.uw),e.Y36(H.Y),e.Y36(pe.c),e.Y36(Ae),e.Y36(Z.T),e.Y36(ce.K),e.Y36(ee.Q),e.Y36(W.N),e.Y36(e.sBO),e.Y36(ye.Kd))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-dashboard"]],viewQuery:function(i,o){if(1&i&&(e.Gf(ae.NW,5),e.Gf(q.YE,5),e.Gf(ke,5)),2&i){let r;e.iGM(r=e.CRH())&&(o.paginator=r.first),e.iGM(r=e.CRH())&&(o.sort=r.first),e.iGM(r=e.CRH())&&(o.projectTable=r.first)}},decls:2,vars:5,consts:[[1,"project-dashboard"],[3,"dataSource","sortConfig","totalCount","pageSize","isLoading","open","edit","delete","share","paginationChanged","sortChange"]],template:function(i,o){1&i&&(e.TgZ(0,"div",0)(1,"app-project-table",1),e.NdJ("open",function(a){return o.onSelectProject(a)})("edit",function(a){return o.openProjectDialog(a)})("delete",function(a){return o.deleteProject(a)})("share",function(a){return o.openShareProjectDialog(a)})("paginationChanged",function(a){return o.onPageChange(a)})("sortChange",function(a){return o.onSortChange(a)}),e.qZA()()),2&i&&(e.xp6(1),e.Q6J("dataSource",o.dataSource)("sortConfig",o.currentSort)("totalCount",o.totalProjectCount)("pageSize",o.pageSize)("isLoading",o.isLoading))},dependencies:[ke],styles:[".project-dashboard[_ngcontent-%COMP%]{padding-inline:.6%;text-align:center;margin-top:calc(var(--toolbar-height) + 10px);overflow:hidden}"]})}return n})();var A=l(6686),pi=l(8337),hi=l(1894),V=l(8445),p=l(5749),u=l(7984),T=l(6750);const gi=[{category:p.J.Project,isDisplay:!0,options:[{label:"Add Diagram",actionName:u.D.AddDiagram},{label:"Add Class",actionName:u.D.AddClass},{label:"Add Associative",actionName:u.D.AddAssociative},{label:"Add Enumeration",actionName:u.D.AddEnumeration},{label:"Add Folder",actionName:u.D.AddFolder}]},{category:T.Uw,isDisplay:!0,options:[{label:"Add Diagram",actionName:u.D.AddDiagram}]},{category:p.J.Diagram,isDisplay:!0,options:[{label:"Edit",actionName:u.D.EditDiagram},{label:"Delete",actionName:u.D.DeleteDiagram}]},{category:T.r$,isDisplay:!0,options:[{label:"Add Class",actionName:u.D.AddClass},{label:"Add Associative",actionName:u.D.AddAssociative}]},{category:p.J.Class,isDisplay:!0,options:[{label:"Add Attribute",actionName:u.D.AddAttribute},{label:"Add Method",actionName:u.D.AddOperation},{label:"Delete",actionName:u.D.DeleteClass}]},{category:p.J.AssociativeClass,isDisplay:!0,options:[{label:"Add Attribute",actionName:u.D.AddAttribute},{label:"Add Method",actionName:u.D.AddOperation},{label:"Delete",actionName:u.D.DeleteClass}]},{category:T.jB,isDisplay:!0,options:[{label:"Add Enumeration",actionName:u.D.AddEnumeration}]},{category:p.J.Enumeration,isDisplay:!0,options:[{label:"Add Literal",actionName:u.D.AddLiteral},{label:"Delete",actionName:u.D.DeleteEnumeration}]},{category:p.J.Folder,isDisplay:!0,options:[{label:"Add Diagram",actionName:u.D.AddDiagram},{label:"Add Class",actionName:u.D.AddClass},{label:"Add Associative",actionName:u.D.AddAssociative},{label:"Add Enumeration",actionName:u.D.AddEnumeration},{label:"Add Folder",actionName:u.D.AddFolder},{label:"Delete",actionName:u.D.DeleteFolder}]},{category:p.J.Attribute,isDisplay:!0,options:[{label:"Delete",actionName:u.D.DeleteAttribute}]},{category:p.J.Operation,isDisplay:!0,options:[{label:"Delete",actionName:u.D.DeleteMethod}]},{category:p.J.EnumerationLiteral,isDisplay:!0,options:[{label:"Delete",actionName:u.D.DeleteLiteral}]}];var he=l(4956),De=l(6405),L=l(3445);function ui(n,s){if(1&n&&(e.TgZ(0,"mat-option",10),e._uU(1),e.qZA()),2&n){const t=s.$implicit;e.Q6J("value",t.id),e.xp6(1),e.hij("",t.name," ")}}function mi(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"mat-form-field",7)(1,"mat-select",8),e.NdJ("selectionChange",function(o){e.CHM(t);const r=e.oxw();return e.KtG(r.onSelectionChange(o))}),e.YNc(2,ui,2,2,"mat-option",9),e.qZA()()}if(2&n){const t=e.oxw();e.xp6(2),e.Q6J("ngForOf",t.attributeTypes)}}let Oe=(()=>{class n{constructor(t,i,o,r){this.dialogRef=t,this.diagramData=i,this._diagramUtils=o,this.sharedService=r,this.attributeTypes=[],this._attributeTypeSub=null}ngOnInit(){this.form=new m.cw({name:new m.NI(this.diagramData.text,[m.kI.required,this.sharedService.noWhitespaceValidator]),dataType:new m.NI(`0_${L.G[L.G.Undefined]}`,[m.kI.required])}),this._attributeTypeSub=this._diagramUtils.getAttributeTypes().subscribe(t=>{this.attributeTypes=t}),this.form.valueChanges.subscribe(t=>{})}ngOnDestroy(){this._attributeTypeSub&&this._attributeTypeSub.unsubscribe()}onNoClick(){this.dialogRef.close()}onYesClick(){this.diagramData.text&&""!==this.diagramData.text.trim()&&this.dialogRef.close(this.diagramData.isAttribute?{name:this.form.value.name,isCreation:this.diagramData.isCreation,dataType:this.form.value.dataType}:{name:this.form.value.name,isCreation:this.diagramData.isCreation})}onSelectionChange(t){this.form.controls.dataType.setValue(t.value)}closeDialogIfValid(){this.diagramData.text&&""!==this.diagramData.text.trim()&&this.dialogRef.close({name:this.diagramData.text,isCreation:this.diagramData.isCreation})}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(k.so),e.Y36(k.WI),e.Y36(Z.T),e.Y36(Se.F))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-diagram-dialog"]],decls:18,vars:16,consts:[["mat-dialog-title","",1,"dialog-title"],[3,"formGroup","ngSubmit"],["matInput","","formControlName","name","required","",3,"keydown.enter"],["appearance","fill",4,"ngIf"],[1,"dialog-buttons"],["mat-flat-button","","cdkFocusInitial","","color","primary",1,"dialog-btn",3,"disabled"],["mat-flat-button","",1,"dialog-btn",3,"mat-dialog-close"],["appearance","fill"],["formControlName","dataType",3,"selectionChange"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(i,o){1&i&&(e.TgZ(0,"h2",0),e._uU(1),e.ALo(2,"translate"),e.qZA(),e.TgZ(3,"form",1),e.NdJ("ngSubmit",function(){return o.onYesClick()}),e.TgZ(4,"mat-dialog-content")(5,"mat-form-field")(6,"mat-label"),e._uU(7),e.ALo(8,"translate"),e.qZA(),e.TgZ(9,"input",2),e.NdJ("keydown.enter",function(){return o.closeDialogIfValid()}),e.qZA()(),e.YNc(10,mi,3,1,"mat-form-field",3),e.qZA(),e.TgZ(11,"mat-dialog-actions",4)(12,"button",5),e._uU(13),e.ALo(14,"translate"),e.qZA(),e.TgZ(15,"button",6),e._uU(16),e.ALo(17,"translate"),e.qZA()()()),2&i&&(e.xp6(1),e.hij(" ",e.lcZ(2,8,o.diagramData.headerTitle),"\n"),e.xp6(2),e.Q6J("formGroup",o.form),e.xp6(4),e.Oqu(e.lcZ(8,10,o.diagramData.placeholder)),e.xp6(3),e.Q6J("ngIf",o.diagramData.isAttribute),e.xp6(2),e.Q6J("disabled",!o.form.valid),e.xp6(1),e.hij(" ",e.lcZ(14,12,o.diagramData.btnText)," "),e.xp6(2),e.Q6J("mat-dialog-close",null),e.xp6(1),e.hij(" ",e.lcZ(17,14,"window.cancel")," "))},dependencies:[v.sg,v.O5,J.lW,D.KE,D.hX,le.Nt,U.ey,k.ZT,k.uh,k.xY,k.H8,X.gD,m._Y,m.Fj,m.JJ,m.JL,m.Q7,m.sg,m.u,S.X$],styles:[".dialog-buttons[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding-inline:1rem}.dialog-buttons[_ngcontent-%COMP%]   .dialog-btn[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;border:1px solid var(--primary-color)}.dialog-title[_ngcontent-%COMP%]{font-size:17px;height:var(--toolbar-height);width:100%;background:var(--primary-color);display:flex;justify-content:flex-start;align-items:center;margin:0;padding:0;padding-inline:1rem;color:#fff}.diagram-field[_ngcontent-%COMP%]{width:100%;margin-top:2rem}.mat-mdc-form-field[_ngcontent-%COMP%]{width:100%}.property-field[_ngcontent-%COMP%]{display:flex!important;flex-direction:column;justify-content:center;margin-inline:.5rem;font-size:14px;position:relative}  .mdc-dialog__content{padding:1rem 1rem 0!important}"]})}return n})();var ge=l(3384),fi=l(7839),_i=l(3138),Ci=l(3920),vi=l(8004),xi=l(5055),te=l(6330),ie=l(3066);let bi=(()=>{class n{constructor(t,i,o,r,a,c,d,g,C,f,h,_,b,P){this.dialog=t,this.snackBarService=i,this.projectService=o,this.diagramService=r,this.diagramUtils=a,this.treeNodeService=c,this.goJsClassService=d,this.gojsEnumerationService=g,this.gojsLiteralService=C,this.gojsAttributeService=f,this.gojsFolderService=h,this.propertyService=_,this._accessService=b,this.router=P,this.diagrams=[],this.projectId=-1,this.baseFileName="class-diagram",this._hasEditAccessOnly=!1,this.projectService.currentProjectChanges().subscribe(y=>{y&&y.id&&(this.projectId=y.id)}),this.diagramUtils.activeDiagramChanges().subscribe(y=>{y&&(this.currentDiagram=y)}),this.diagramUtils.currentProjectDiagramsChanges().subscribe(y=>{this.diagrams=y}),this._accessService.accessTypeChanges().subscribe(y=>{this._hasEditAccessOnly=y!=x.WP.Viewer})}executeAction(t,i,o){switch(t){case u.D.AddDiagram:this.openDiagramDialog({name:i.data?.name,targetedNode:i},!0);break;case u.D.EditDiagram:this.openDiagramDialog({name:i.data?.name,id:i.data?.id},!1);break;case u.D.DeleteDiagram:this.onDeleteDiagram(i);break;case u.D.AddLiteral:i.data&&"idTemplateEnumeration"in i.data&&this.openNodeCreationDialog({headerTitle:"literal.header",placeholder:"literal.placeholder",text:"Literal",btnText:"",isCreation:!0},i,t);break;case u.D.DeleteFolder:case u.D.DeleteClass:case u.D.DeleteEnumeration:case u.D.DeleteAttribute:case u.D.DeleteMethod:case u.D.DeleteLiteral:const r=o.isSelected(i)?[...o.selected]:[...o.selected,i];this.deleteSelectedNodes(r);break;case u.D.AddAttribute:i.data&&"idTemplateClass"in i.data&&this.openNodeCreationDialog({headerTitle:"attribute.header",placeholder:"attribute.placeholder",text:"Attribute",btnText:"",isCreation:!0,isAttribute:!0},i,t);break;case u.D.AddOperation:i.data&&"idTemplateClass"in i.data&&this.openNodeCreationDialog({headerTitle:"method.header",placeholder:"method.placeholder",text:"Method",btnText:"",isCreation:!0,isAttribute:!0},i,t);break;case u.D.AddFolder:this.openNodeCreationDialog({headerTitle:"folder.header",placeholder:"folder.placeholder",text:"New Folder",btnText:"",isCreation:!0},i,t);break;case u.D.AddClass:case u.D.AddAssociative:this.openNodeCreationDialog({headerTitle:"class.header",placeholder:"class.placeholder",text:"Class",btnText:"",isCreation:!0},i,t);break;case u.D.AddEnumeration:this.openNodeCreationDialog({headerTitle:"enumeration.header",placeholder:"enumeration.placeholder",text:"Enumeration",btnText:"",isCreation:!0},i,t)}}openNodeCreationDialog(t,i,o){this.dialog.open(Oe,{width:"300px",data:{text:t.text||"",headerTitle:t.headerTitle,placeholder:t.placeholder,btnText:"diagram.create",isCreation:!0,isAttribute:t.isAttribute}}).afterClosed().subscribe(a=>{if(a)switch(o){case u.D.AddFolder:this.gojsFolderService.onCreateNewFolder(a.name,this.projectId,this._hasEditAccessOnly,i.tag);break;case u.D.AddClass:case u.D.AddAssociative:this.goJsClassService.handleClassCreationFromLibrary(a.name,i,o===u.D.AddAssociative);break;case u.D.AddEnumeration:this.gojsEnumerationService.handleEnumerationCreationFromLibrary(a.name,i);break;case u.D.AddAttribute:i.data&&"idTemplateClass"in i.data&&this.gojsAttributeService.addAttributeOrMethodFromLibrary(a.name,p.J.Attribute,i.data.idTemplateClass,a.dataType);break;case u.D.AddOperation:i.data&&"idTemplateClass"in i.data&&this.gojsAttributeService.addAttributeOrMethodFromLibrary(a.name,p.J.Operation,i.data.idTemplateClass,a.dataType);break;case u.D.AddLiteral:this.gojsLiteralService.addLiteralInLibrary(a.name,i.data)}})}openDiagramDialog(t,i){this.dialog.open(Oe,{width:"300px",data:{text:i?this.generateFileName():t.name||"",headerTitle:i?"diagram.createDiagram":"diagram.editDiagram",placeholder:"diagram.diagramPlaceholderText",btnText:i?"diagram.create":"diagram.save",isCreation:i}}).afterClosed().subscribe(r=>{r&&(r.isCreation?this.diagramService.createDiagram({idProject:this.projectId,name:r.name,idFolder:t.targetedNode&&t.targetedNode.category==p.J.Folder?t.targetedNode.data?.idFolder:0}).subscribe(a=>{this.diagrams.push(a),this.diagramUtils.setCurrentProjectDiagrams(this.diagrams),this.diagramUtils.setActiveDiagram(a),this.treeNodeService.addGroupNodeInTree({name:a.name,children:[],category:p.J.Diagram,icon:De.n.Diagram,tag:`atTag${p.J.Diagram}_${a.id}`,parentTag:t.targetedNode&&t.targetedNode.category==p.J.Project?`${u.M.DiagramWrapper}_${u.M.Project}`:`${t.targetedNode?.tag}`,data:a,isDraggable:!0,supportingNodes:[p.J.Diagram]}),this.propertyService.setPropertyData(null),a.id&&(this.currentDiagram=a,this.router.navigate([`/editor/${this.projectId}/diagram/${a.id}`],{replaceUrl:!0}))}):this.updateDiagram(t.id,r.name))})}updateDiagram(t,i){this._hasEditAccessOnly&&this.diagramService.updateDiagram({id:t,name:i}).subscribe(o=>{const r=this.diagrams.findIndex(a=>a.id==this.currentDiagram.id);-1!==r&&(this.treeNodeService.editGroupTreeNode({name:o.name,children:[],category:p.J.Diagram,icon:De.n.Diagram,tag:`atTag${p.J.Diagram}_${o.id}`,data:{...this.diagrams[r],name:o.name},isDraggable:!0,supportingNodes:[p.J.Diagram]}),this.diagrams[r].name=o.name),this.diagramUtils.setActiveDiagram(this.diagrams[r]),this.snackBarService.openSnackbar("snackBar.diagramUpdateMsg")})}generateFileName(){const t=new Set(this.diagrams.map(r=>r.name));let i=this.baseFileName,o=1;for(;t.has(i);)i=`${this.baseFileName}${o}`,o++;return i}onDeleteDiagram(t){this.dialog.open(Q.X,{width:"320px",data:{title:"dialog.title",reject:"dialog.no",confirm:"dialog.yes"}}).afterClosed().subscribe(o=>{o&&t&&t.data?.id&&(this.diagramService.deleteDiagram(t.data?.id),this.treeNodeService.deleteGroupTreeNode(t),this.diagrams=this.diagrams.filter(r=>r.id!==t.data?.id),this.diagramUtils.setCurrentProjectDiagrams(this.diagrams),this.diagrams.length>0?(this.currentDiagram=this.diagrams[0],this.diagramUtils.setActiveDiagram(this.currentDiagram),this.router.navigate([`/editor/${this.projectId}/diagram/${this.currentDiagram.id}`],{replaceUrl:!0})):(this.diagramUtils.setActiveDiagram(null),this.router.navigate([`/editor/${this.projectId}/diagram/0`],{replaceUrl:!0})))})}renameNode(t){if(0!==t.name.trim().length)switch(t.category){case p.J.Diagram:this.updateDiagram(t.data?.id,t.name);break;case p.J.Class:case p.J.AssociativeClass:this.goJsClassService.handleEditClassNameInLibrary(t.data,t);break;case p.J.Enumeration:this.gojsEnumerationService.handleEditEnumNameInLibrary(t.data,t);break;case p.J.Folder:this.gojsFolderService.handleFolderEdit(t.data,t);break;case p.J.Attribute:case p.J.Operation:this.gojsAttributeService.handleEditAttributeNameInLibrary(t.data,t);break;case p.J.EnumerationLiteral:this.gojsLiteralService.handleEditAttributeNameInLibrary(t.data,t)}}deleteSelectedNodes(t){this.doDeleteSelectedNodes(t),t.forEach(i=>{this.treeNodeService.deleteGroupTreeNode(i)}),this.propertyService.setPropertyData(null)}doDeleteSelectedNodes(t){const i=[],o=[],r=[],a=[],c=[];t.forEach(d=>{switch(d.category){case p.J.Class:case p.J.AssociativeClass:this.treeNodeService.nodeExistOrNot(d.parentTag,t)||i.push(d.data);break;case p.J.Enumeration:this.treeNodeService.nodeExistOrNot(d.parentTag,t)||o.push(d.data);break;case p.J.Attribute:case p.J.Operation:this.checkNodeExists(d.parentTag,t)||r.push(d.data);break;case p.J.EnumerationLiteral:this.checkNodeExists(d.parentTag,t)||a.push(d.data);break;case p.J.Folder:this.treeNodeService.nodeExistOrNot(d.tag,t)||c.push(d)}}),i.length>0&&this.goJsClassService.deleteTempClass(i),o.length>0&&this.gojsEnumerationService.deleteTempEnumeration(o),r.length>0&&this.gojsAttributeService.deleteMultipleAttrInPalette(r),a.length>0&&this.gojsLiteralService.deletePaletteLiteral(a),c.length>0&&this.gojsFolderService.deleteSelectedFolder(c)}checkNodeExists(t,i){return i.some(o=>o.tag===t)}static#e=this.\u0275fac=function(i){return new(i||n)(e.LFG(k.uw),e.LFG(pe.c),e.LFG(H.Y),e.LFG(ge.p),e.LFG(Z.T),e.LFG(he.G),e.LFG(fi.g),e.LFG(_i.C),e.LFG(Ci.u),e.LFG(vi.C),e.LFG(xi._),e.LFG(te.b),e.LFG(ie.v),e.LFG(M.F0))};static#t=this.\u0275prov=e.Yz7({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var ue=l(7225),Ee=l(9038);function yi(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"app-search-bar",9),e.NdJ("searchChanged",function(o){e.CHM(t);const r=e.oxw();return e.KtG(r.onSearch(o))}),e.ALo(1,"translate"),e.qZA()}2&n&&e.s9C("placeholder",e.lcZ(1,1,"diagram.search"))}function Pi(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"mat-list-item",12),e.NdJ("click",function(o){const a=e.CHM(t).$implicit,c=e.oxw(2);return e.KtG(c.selectDiagramNode(o,a))}),e._UZ(1,"span",13),e.TgZ(2,"span",14),e._uU(3),e.ALo(4,"truncate"),e.qZA()()}if(2&n){const t=s.$implicit,i=e.oxw(2);e.ekj("selected-diagram",t.tag===i.currentDiagramTag()),e.xp6(1),e.Q6J("innerHTML",t.icon,e.oJD),e.xp6(1),e.Q6J("matTooltip",i.shouldShowTooltip(t.name,25)?t.name:""),e.xp6(1),e.hij(" ",e.xi3(4,5,t.name,25)," ")}}function wi(n,s){if(1&n&&(e.TgZ(0,"div",10)(1,"h3"),e._uU(2),e.ALo(3,"translate"),e.qZA(),e.TgZ(4,"mat-list"),e.YNc(5,Pi,5,8,"mat-list-item",11),e.qZA()()),2&n){const t=e.oxw();e.xp6(2),e.Oqu(e.lcZ(3,2,"diagram.diagramsList")),e.xp6(3),e.Q6J("ngForOf",t.searchResults())}}function ki(n,s){if(1&n&&(e.ynx(0),e.TgZ(1,"span",14),e._uU(2),e.ALo(3,"truncate"),e.qZA(),e.BQk()),2&n){const t=e.oxw().$implicit,i=e.oxw(2);e.xp6(1),e.Q6J("matTooltip",i.shouldShowTooltip(t.name,25)?t.name:""),e.xp6(1),e.hij(" ",e.xi3(3,2,t.name,25),"")}}function Ti(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"input",20,21),e.NdJ("ngModelChange",function(o){e.CHM(t);const r=e.oxw().$implicit;return e.KtG(r.name=o)})("blur",function(){e.CHM(t);const o=e.MAs(1),r=e.oxw().$implicit,a=e.oxw(2);return e.KtG(a.saveRename(r,o.value))})("keydown.enter",function(){e.CHM(t);const o=e.MAs(1),r=e.oxw().$implicit,a=e.oxw(2);return e.KtG(a.saveRename(r,o.value))})("keydown.escape",function(){e.CHM(t);const o=e.oxw().$implicit,r=e.oxw(2);return e.KtG(r.cancelRename(o))}),e.qZA()}if(2&n){const t=e.oxw().$implicit;e.Q6J("ngModel",t.name)}}function Ai(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"mat-list-item",17),e.NdJ("contextmenu",function(o){const a=e.CHM(t).$implicit,c=e.oxw(2);return e.KtG(c.onRightClick(o,a))})("dragstart",function(o){const a=e.CHM(t).$implicit,c=e.oxw(2);return e.KtG(c.onDragStart(o,a))})("click",function(o){const a=e.CHM(t).$implicit,c=e.oxw(2);return e.KtG(c.selectDiagramNode(o,a))})("dragover",function(o){const a=e.CHM(t).$implicit,c=e.oxw(2);return e.KtG(c.onDragOver(o,a))})("drop",function(o){const a=e.CHM(t).$implicit,c=e.oxw(2);return e.KtG(c.onMove(o,a))})("dblclick",function(){const r=e.CHM(t).$implicit,a=e.oxw(2);return e.KtG(a.enableRename(r))}),e._UZ(1,"span",13),e.YNc(2,ki,4,5,"ng-container",18),e.YNc(3,Ti,2,1,"ng-template",null,19,e.W1O),e.qZA()}if(2&n){const t=s.$implicit,i=e.MAs(4),o=e.oxw(2);e.ekj("selected",o.selection.isSelected(t))("selected-diagram",t.tag===o.currentDiagramTag()),e.Q6J("draggable",t.isDraggable&&o.hasEditAccessOnly()&&o.hasDiagram()),e.uIk("data-node-tag",t.tag),e.xp6(1),e.Q6J("innerHTML",t.icon,e.oJD),e.xp6(1),e.Q6J("ngIf",!t.isRenaming)("ngIfElse",i)}}function Si(n,s){if(1&n&&(e.TgZ(0,"div",15)(1,"mat-list"),e.YNc(2,Ai,5,9,"mat-list-item",16),e.qZA()()),2&n){const t=e.oxw();e.xp6(2),e.Q6J("ngForOf",t.searchResults())}}function Mi(n,s){1&n&&(e.TgZ(0,"div",15)(1,"p",23),e._uU(2),e.ALo(3,"translate"),e.qZA()()),2&n&&(e.xp6(2),e.Oqu(e.lcZ(3,1,"diagram.notFound")))}function Di(n,s){if(1&n&&e.YNc(0,Mi,4,3,"div",22),2&n){const t=e.oxw();e.Q6J("ngIf",0===t.searchResults().length&&t.isSearching()&&!t.showOnlyDiagrams())}}function Oi(n,s){if(1&n&&(e.ynx(0),e.TgZ(1,"span",14),e._uU(2),e.ALo(3,"truncate"),e.qZA(),e.BQk()),2&n){const t=e.oxw().$implicit,i=e.oxw();e.xp6(1),e.Q6J("matTooltip",i.shouldShowTooltip(t.name,25)?t.name:""),e.xp6(1),e.Oqu(e.xi3(3,2,t.name,25))}}function Ei(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"input",20,21),e.NdJ("ngModelChange",function(o){e.CHM(t);const r=e.oxw().$implicit;return e.KtG(r.name=o)})("blur",function(){e.CHM(t);const o=e.MAs(1),r=e.oxw().$implicit,a=e.oxw();return e.KtG(a.saveRename(r,o.value))})("keydown.enter",function(){e.CHM(t);const o=e.MAs(1),r=e.oxw().$implicit,a=e.oxw();return e.KtG(a.saveRename(r,o.value))})("keydown.escape",function(){e.CHM(t);const o=e.oxw().$implicit,r=e.oxw();return e.KtG(r.cancelRename(o))}),e.qZA()}if(2&n){const t=e.oxw().$implicit;e.Q6J("ngModel",t.name)}}function Ii(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"mat-tree-node",24),e.NdJ("contextmenu",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onRightClick(o,a))})("dragstart",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onDragStart(o,a))})("click",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.selectDiagramNode(o,a))})("dragover",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onDragOver(o,a))})("drop",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onMove(o,a))})("dblclick",function(){const r=e.CHM(t).$implicit,a=e.oxw();return e.KtG(a.enableRename(r))}),e.TgZ(1,"span",25),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw();return e.KtG(a.treeControl.toggle(r))}),e._UZ(2,"span",26),e.YNc(3,Oi,4,5,"ng-container",18),e.YNc(4,Ei,2,1,"ng-template",null,19,e.W1O),e.qZA()()}if(2&n){const t=s.$implicit,i=e.MAs(5),o=e.oxw();e.ekj("selected",o.selection.isSelected(t))("selected-diagram",t.tag===o.currentDiagramTag()),e.Q6J("draggable",t.isDraggable&&o.hasEditAccessOnly()&&o.hasDiagram()),e.uIk("data-node-tag",t.tag),e.xp6(2),e.Q6J("innerHTML",t.icon,e.oJD)("draggable",t.isDraggable&&o.hasEditAccessOnly()),e.xp6(1),e.Q6J("ngIf",!t.isRenaming)("ngIfElse",i)}}function ji(n,s){if(1&n&&(e.ynx(0),e.TgZ(1,"span",14),e._uU(2),e.ALo(3,"truncate"),e.qZA(),e.BQk()),2&n){const t=e.oxw().$implicit,i=e.oxw();e.xp6(1),e.Q6J("matTooltip",i.shouldShowTooltip(t.name,25)?t.name:""),e.xp6(1),e.Oqu(e.xi3(3,2,t.name,25))}}function Ni(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"input",20,21),e.NdJ("ngModelChange",function(o){e.CHM(t);const r=e.oxw().$implicit;return e.KtG(r.name=o)})("blur",function(){e.CHM(t);const o=e.MAs(1),r=e.oxw().$implicit,a=e.oxw();return e.KtG(a.saveRename(r,o.value))})("keydown.enter",function(){e.CHM(t);const o=e.MAs(1),r=e.oxw().$implicit,a=e.oxw();return e.KtG(a.saveRename(r,o.value))})("keydown.escape",function(){e.CHM(t);const o=e.oxw().$implicit,r=e.oxw();return e.KtG(r.cancelRename(o))}),e.qZA()}if(2&n){const t=e.oxw().$implicit;e.Q6J("ngModel",t.name)}}function Zi(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"mat-nested-tree-node",27),e.NdJ("click",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.selectDiagramNode(o,a))}),e.TgZ(1,"div",28),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw();return e.KtG(a.treeControl.toggle(r))})("contextmenu",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onRightClick(o,a))})("dragstart",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onDragStart(o,a))})("dragenter",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onDragEnter(o,a))})("dragleave",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onDragLeave(o,a))})("dragover",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onDragOver(o,a))})("drop",function(o){const a=e.CHM(t).$implicit,c=e.oxw();return e.KtG(c.onMove(o,a))})("dblclick",function(){const r=e.CHM(t).$implicit,a=e.oxw();return e.KtG(a.enableRename(r))}),e.TgZ(2,"mat-icon",29),e._uU(3),e.qZA(),e.TgZ(4,"span",30),e._UZ(5,"span",26),e.YNc(6,ji,4,5,"ng-container",18),e.YNc(7,Ni,2,1,"ng-template",null,31,e.W1O),e.qZA()(),e.TgZ(9,"div",32),e.GkF(10,33),e.qZA()()}if(2&n){const t=s.$implicit,i=e.MAs(8),o=e.oxw();e.uIk("data-node-tag",t.tag),e.xp6(1),e.ekj("drop-target",o.isDraggedOver())("can-drop",o.isDraggedOver()&&o.isValidDropTarget(t))("cannot-drop",o.isDraggedOver()&&!o.isValidDropTarget(t))("dragging",o.isDragging()&&o.draggedNode()===t)("selected",o.selection.isSelected(t)),e.Q6J("draggable",t.isDraggable&&o.hasEditAccessOnly()&&o.hasDiagram()),e.xp6(1),e.uIk("aria-label","Toggle "+t.name),e.xp6(1),e.hij(" ",o.treeControl.isExpanded(t)?"expand_more":"chevron_right"," "),e.xp6(2),e.Q6J("innerHTML",t.icon,e.oJD)("draggable",t.isDraggable&&o.hasEditAccessOnly()),e.xp6(1),e.Q6J("ngIf",!t.isRenaming)("ngIfElse",i),e.xp6(3),e.ekj("library-tree-invisible",!o.treeControl.isExpanded(t))}}function Vi(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"button",36),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(2);return e.KtG(a.onAction(r.actionName))}),e._uU(1),e.qZA()}if(2&n){const t=s.$implicit;e.xp6(1),e.hij(" ",t.label," ")}}function Fi(n,s){if(1&n&&(e.ynx(0),e.TgZ(1,"div",34),e.YNc(2,Vi,2,1,"button",35),e.qZA(),e.BQk()),2&n){const t=e.oxw();e.xp6(1),e.Q6J("ngStyle",t.getRightClickMenuStyle()),e.xp6(1),e.Q6J("ngForOf",t.contextMenuOptions())}}let Ie=(()=>{class n{onDeleteKey(){this.hasEditAccessOnly()&&this.selection.selected.length>0&&this.deleteSelectedNodes()}set expandNodeTag(t){t&&setTimeout(()=>{this.expandNodeByTag(t)},200)}constructor(t,i,o,r,a,c,d,g,C){this._dialog=t,this.treeNodeService=i,this.diagramUtils=o,this._contextMenuActionService=r,this._accessService=a,this._propertyService=c,this._navbarService=d,this._ngZone=g,this.router=C,this.treeControl=new hi.VY(f=>f.children),this.dataSource=new V.WX,this._isDisplayContextMenu=(0,e.tdS)(!1),this._rightClickMenuPositionX=(0,e.tdS)(0),this._rightClickMenuPositionY=(0,e.tdS)(0),this._currentDiagram=(0,e.tdS)(null),this._currentNode=(0,e.tdS)(null),this._contextMenuOptions=(0,e.tdS)([]),this._hasEditAccessOnly=(0,e.tdS)(!1),this._draggedNode=(0,e.tdS)(null),this._treeNodeData=(0,e.tdS)(null),this._selectedTreeNode=(0,e.tdS)(null),this._selectedNodes=(0,e.tdS)(new Set),this._isRenaming=(0,e.tdS)(!1),this._previousName=(0,e.tdS)(""),this._currentDiagramTag=(0,e.tdS)(""),this._searchResults=(0,e.tdS)([]),this._hasDiagram=(0,e.tdS)(!0),this._isSearching=(0,e.tdS)(!1),this._showOnlyDiagrams=(0,e.tdS)(!1),this._isDraggedOver=(0,e.tdS)(!1),this._isDragging=(0,e.tdS)(!1),this._currentDragTarget=(0,e.tdS)(null),this.isDisplayContextMenu=this._isDisplayContextMenu.asReadonly(),this.rightClickMenuPositionX=this._rightClickMenuPositionX.asReadonly(),this.rightClickMenuPositionY=this._rightClickMenuPositionY.asReadonly(),this.currentDiagram=this._currentDiagram.asReadonly(),this.contextMenuOptions=this._contextMenuOptions.asReadonly(),this.hasEditAccessOnly=this._hasEditAccessOnly.asReadonly(),this.draggedNode=this._draggedNode.asReadonly(),this.treeNodeData=this._treeNodeData.asReadonly(),this.selectedTreeNode=this._selectedTreeNode.asReadonly(),this.selectedNodes=this._selectedNodes.asReadonly(),this.isRenaming=this._isRenaming.asReadonly(),this.previousName=this._previousName.asReadonly(),this.currentDiagramTag=this._currentDiagramTag.asReadonly(),this.searchResults=this._searchResults.asReadonly(),this.hasDiagram=this._hasDiagram.asReadonly(),this.isSearching=this._isSearching.asReadonly(),this.showOnlyDiagrams=this._showOnlyDiagrams.asReadonly(),this.isDraggedOver=this._isDraggedOver.asReadonly(),this.isDragging=this._isDragging.asReadonly(),this.currentDragTarget=this._currentDragTarget.asReadonly(),this.isTreeReady=(0,e.Flj)(()=>null!==this._treeNodeData()&&this.dataSource.data.length>0),this.selection=new pi.Ov(!0,[]),this.hasChild=(f,h)=>!!h.children&&h.children.length>0,(0,e.cEC)(()=>{const f=this._navbarService.showVersionHistory();this._ngZone.run(()=>{this.toggleDiagramsView(f)})},{allowSignalWrites:!0}),this.treeNodeService.getLibraryDetails().subscribe(f=>{if(f){this._treeNodeData.set(f),this.dataSource.data=[],this.dataSource.data=[f];const h=this.treeControl.getDescendants(f);this.treeNodeService.descendantTreeNodes=h;const _=this._currentDiagramTag();_&&setTimeout(()=>{this.expandNodeByTag(_)},100)}}),this.diagramUtils.activeDiagramChanges().subscribe(f=>{f?(this._hasDiagram.set(!0),this._currentDiagram.set(f),this._currentDiagramTag.set(`atTag${p.J.Diagram}_${f.id}`),setTimeout(()=>{this.expandNodeByTag(this._currentDiagramTag())},100)):(this._propertyService.setPropertyData(null),this._hasDiagram.set(!1))}),this._accessService.accessTypeChanges().subscribe(f=>{this._hasEditAccessOnly.set(f!=x.WP.Viewer)})}ngOnInit(){}onSearch(t){t?(this._isSearching.set(!0),this._searchResults.set(this.searchNodes(t)),this._propertyService.setPropertyData(null)):(this._isSearching.set(!1),this._searchResults.set([]))}toggleDiagramsView(t){this._showOnlyDiagrams.set(t),this.showOnlyDiagrams()&&this.populateDiagramsList()}populateDiagramsList(){this._searchResults.set(this.getAllNodes().filter(t=>t.category===p.J.Diagram))}searchNodes(t,i=this.dataSource.data){let o=[];for(const r of i)this.nodeFilterCriteria(r)&&r.name.toLowerCase().includes(t.toLowerCase())&&o.push(r),r.children&&r.children.length>0&&(o=o.concat(this.searchNodes(t,r.children)));return o}nodeFilterCriteria(t){return t.category!==T.r$&&t.category!==T.jB&&t.category!==T.Uw&&t.category!==p.J.Project}clearSearch(){this._searchResults.set([]),this._propertyService.setPropertyData(null)}selectSearchResult(t,i){this.selection.select(i),this.selectDiagramNode(t,i)}deleteSelectedNodes(){const t=this.selection.selected;0!==t.length&&this._dialog.open(Q.X,{width:"320px",data:{title:"dialog.title",reject:"dialog.no",confirm:"dialog.yes"}}).afterClosed().subscribe(o=>{o&&(this._contextMenuActionService.deleteSelectedNodes(t),this.selection.clear())})}canDiagramDraggable(t){if(t&&t.category===p.J.Diagram){const i=this.treeNodeData();if(i)return this.treeNodeService.findParentNode(t.parentTag,i),!0}return!0}onRightClick(t,i){t.stopPropagation(),t.preventDefault(),this._currentNode.set(i);const o=gi.find(r=>r.category===i.category&&r.isDisplay);this.selection.selected.length>1&&!this.selection.isSelected(i)&&(this.selection.clear(),this.selection.select(i)),o&&(this._contextMenuOptions.set(this.selection.selected.length<=1?o.options:o.options.filter(r=>"Delete"==r.label)),this._isDisplayContextMenu.set(!0),this._rightClickMenuPositionX.set(t.clientX),this._rightClickMenuPositionY.set(t.clientY))}documentClick(){this._isDisplayContextMenu.set(!1),this.selection.clear()}getRightClickMenuStyle(){return{position:"fixed",left:`${this.rightClickMenuPositionX()}px`,top:`${this.rightClickMenuPositionY()}px`}}selectDiagramNode(t,i){if(t.stopPropagation(),this._isDisplayContextMenu.set(!1),i.category!==T.r$&&i.category!==T.jB&&i.category!==T.Uw&&i.category!==p.J.Project&&this.hasDiagram()){if(this._selectedTreeNode.set(i),!this.isDiagram(i.data)&&i.data)if(this._propertyService.setPropertyData(i.data),t.ctrlKey||t.metaKey)this.selection.toggle(i);else if(t.shiftKey&&this.selection.selected.length>0){const o=this.selection.selected[this.selection.selected.length-1],r=this.getAllNodes(),a=r.indexOf(o),c=r.indexOf(i);r.slice(Math.min(a,c),Math.max(a,c)+1).forEach(g=>{g.category===T.r$||g.category===T.jB||g.category===T.Uw||g.category===p.J.Project||g.category===p.J.Diagram||this.selection.select(g)})}else this.selection.clear(),this.selection.select(i);if(i.category===p.J.Diagram&&this.currentDiagram()?.id!==i.data?.id){this._propertyService.setPropertyData(null);const o=i.data;this.diagramUtils.setActiveDiagram(o);const r=o.idProject,a=o.id;r&&a&&this.router.navigate([`/editor/${r}/diagram/${a}`],{replaceUrl:!0})}}else this._propertyService.setPropertyData(null)}getAllNodes(){const t=[],i=o=>{o.forEach(r=>{t.push(r),r.children&&i(r.children)})};return i(this.dataSource.data),t}isDiagram(t){return void 0!==t.id&&void 0!==t.idProject&&void 0===t.category}onDragStart(t,i){t.dataTransfer&&(t.dataTransfer.setData("text/plain",JSON.stringify(i)),this._draggedNode.set(i),this._isDragging.set(!0),t.target.classList.add("dragging"))}onAction(t){const i=this._currentNode();i&&this._contextMenuActionService.executeAction(t,i,this.selection)}onDragOver(t,i){t.preventDefault(),t.dataTransfer.dropEffect=this.isValidDropTarget(i)?"move":"none"}onMove(t,i){if(t.preventDefault(),this._isDraggedOver.set(!1),this._isDragging.set(!1),this._currentDragTarget.set(null),this.isValidDropTarget(i)){const o=this.draggedNode();o&&this.treeNodeService.moveNode(i,o)}}isSelected(t){return this.selectedNodes().has(t)}onRenameF2Key(){const t=this.selectedTreeNode();this.hasEditAccessOnly()&&t&&this.hasDiagram()&&this.enableRename(t)}canRename(t){return!(t.category==T.Uw||t.category==T.r$||t.category==T.jB||t.category===p.J.Project)}enableRename(t){!this.hasEditAccessOnly()||!this.hasDiagram()||(this.canRename(t)&&(t.isRenaming=!0,this._previousName.set(t.name)),setTimeout(()=>{const i=document.querySelector(".rename-input");i&&this.selectedTreeNode()&&(i.focus(),i.select())}))}saveRename(t,i){const o=i.trim(),r=this.previousName();if(""===o||o===r){t.name=r||t.name;const a=document.querySelector(".rename-input");a&&a.blur()}else t.name=o,this._contextMenuActionService.renameNode(t);t.isRenaming=!1,this._previousName.set("")}cancelRename(t){t.isRenaming=!1}shouldShowTooltip(t,i){return t.length>i}onDragEnter(t,i){t.preventDefault(),this._currentDragTarget.set(i),this._isDraggedOver.set(!0)}onDragLeave(t,i){t.preventDefault(),this.currentDragTarget()===i&&(this._isDraggedOver.set(!1),this._currentDragTarget.set(null))}isValidDropTarget(t){const i=this.draggedNode();return!!(i&&t.supportingNodes?.includes(i.category)&&!t.category.includes("Wrapper")&&this.hasDiagram()&&this.canDiagramDraggable(i))}ngOnDestroy(){this._isDraggedOver.set(!1),this._isDragging.set(!1),this._currentDragTarget.set(null)}expandNodeByTag(t){if(!this.dataSource.data||0===this.dataSource.data.length)return void setTimeout(()=>this.expandNodeByTag(t),200);const i=this.treeNodeService.findNodeByTag(t);i&&(this.expandParentNodes(i),this.hasChild(0,i)&&!this.treeControl.isExpanded(i)&&this.treeControl.expand(i),setTimeout(()=>{const o=document.querySelector(`[data-node-tag="${t}"]`);o&&o.scrollIntoView({behavior:"smooth",block:"center"})},200))}expandParentNodes(t){this.getParentChain(t).forEach(o=>{this.treeControl.isExpanded(o)||this.treeControl.expand(o)})}getParentChain(t){const i=[];let o=t;for(;o.parentTag;){const r=this.treeNodeService.findNodeByTag(o.parentTag);if(!r)break;i.unshift(r),o=r}return i}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(k.uw),e.Y36(he.G),e.Y36(Z.T),e.Y36(bi),e.Y36(ie.v),e.Y36(te.b),e.Y36(ue.I),e.Y36(e.R0b),e.Y36(M.F0))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-library-tree"]],hostBindings:function(i,o){1&i&&e.NdJ("keyup.delete",function(a){return o.onDeleteKey(a)},!1,e.Jf7)("click",function(){return o.documentClick()},!1,e.evT)("keyup.f2",function(){return o.onRenameF2Key()},!1,e.Jf7)},inputs:{expandNodeTag:"expandNodeTag"},decls:10,vars:9,consts:[["class","search-container","backgroundColor","white",3,"placeholder","searchChanged",4,"ngIf"],["class","diagrams-list-view",4,"ngIf"],["class","search-results",4,"ngIf","ngIfElse"],["notFoundTemplate",""],[3,"hidden"],[1,"library-tree",3,"dataSource","treeControl"],["matTreeNodeToggle","","class","tree-node node-content",3,"selected","selected-diagram","draggable","contextmenu","dragstart","click","dragover","drop","dblclick",4,"matTreeNodeDef"],["class","tree-node",3,"click",4,"matTreeNodeDef","matTreeNodeDefWhen"],[4,"ngIf"],["backgroundColor","white",1,"search-container",3,"placeholder","searchChanged"],[1,"diagrams-list-view"],["class","diagram-list-item",3,"selected-diagram","click",4,"ngFor","ngForOf"],[1,"diagram-list-item",3,"click"],[1,"nodeIcon",3,"innerHTML"],[3,"matTooltip"],[1,"search-results"],["class","search-result-item",3,"selected","selected-diagram","draggable","contextmenu","dragstart","click","dragover","drop","dblclick",4,"ngFor","ngForOf"],[1,"search-result-item",3,"draggable","contextmenu","dragstart","click","dragover","drop","dblclick"],[4,"ngIf","ngIfElse"],["renameInput",""],["type","text",1,"rename-input",3,"ngModel","ngModelChange","blur","keydown.enter","keydown.escape"],["renameInputElement",""],["class","search-results",4,"ngIf"],[1,"not-found-text"],["matTreeNodeToggle","",1,"tree-node","node-content",3,"draggable","contextmenu","dragstart","click","dragover","drop","dblclick"],[1,"node-content",3,"click"],[1,"nodeIcon",3,"innerHTML","draggable"],[1,"tree-node",3,"click"],[1,"mat-tree-node","node-content",3,"draggable","click","contextmenu","dragstart","dragenter","dragleave","dragover","drop","dblclick"],["matTreeNodeToggle",""],[1,"node-content"],["nestedRenameInput",""],["role","group"],["matTreeNodeOutlet",""],[1,"menu-link","mat-elevation-z4",3,"ngStyle"],["mat-menu-item","",3,"click",4,"ngFor","ngForOf"],["mat-menu-item","",3,"click"]],template:function(i,o){if(1&i&&(e.YNc(0,yi,2,3,"app-search-bar",0),e.YNc(1,wi,6,4,"div",1),e.YNc(2,Si,3,1,"div",2),e.YNc(3,Di,1,1,"ng-template",null,3,e.W1O),e.TgZ(5,"div",4)(6,"mat-tree",5),e.YNc(7,Ii,6,10,"mat-tree-node",6),e.YNc(8,Zi,11,20,"mat-nested-tree-node",7),e.qZA()(),e.YNc(9,Fi,3,2,"ng-container",8)),2&i){const r=e.MAs(4);e.Q6J("ngIf",!o.showOnlyDiagrams()),e.xp6(1),e.Q6J("ngIf",o.showOnlyDiagrams()),e.xp6(1),e.Q6J("ngIf",o.searchResults().length>0&&o.isSearching()&&!o.showOnlyDiagrams())("ngIfElse",r),e.xp6(3),e.Q6J("hidden",o.isSearching()||o.showOnlyDiagrams()),e.xp6(1),e.Q6J("dataSource",o.dataSource)("treeControl",o.treeControl),e.xp6(2),e.Q6J("matTreeNodeDefWhen",o.hasChild),e.xp6(1),e.Q6J("ngIf",o.isDisplayContextMenu()&&o.hasEditAccessOnly())}},dependencies:[v.sg,v.O5,v.PC,Y.gM,j.Hw,N.OP,V.GZ,V.fQ,V.eu,V.gi,V.uo,V.Ar,Ee.i$,Ee.Tg,m.Fj,m.JJ,m.On,Pe,S.X$,we],styles:['.library-tree[_ngcontent-%COMP%]{padding-inline:.2rem;background-color:transparent;font-size:13px;align-items:center;max-height:300px}.library-tree-invisible[_ngcontent-%COMP%]{display:none}.library-tree[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .library-tree[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-top:0;margin-bottom:0;list-style-type:none}.library-tree[_ngcontent-%COMP%]   .mat-nested-tree-node[_ngcontent-%COMP%]   div[role=group][_ngcontent-%COMP%]{padding-left:.9rem}.library-tree[_ngcontent-%COMP%]   div[role=group][_ngcontent-%COMP%] > .mat-tree-node[_ngcontent-%COMP%]{padding-left:1.4rem;margin-top:.18rem}.library-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]:hover{background-color:#0000000a}.library-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]   .node-content[_ngcontent-%COMP%]{border-radius:4px;cursor:pointer;display:flex;justify-content:flex-start;align-items:center;transition:background-color .3s ease}.library-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]   .node-content[_ngcontent-%COMP%]:hover{background-color:#e0e0e0}.library-tree[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]{background-color:#add8e6}.library-tree[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]:hover{background-color:#00000014}.library-tree[_ngcontent-%COMP%]   .selected-diagram[_ngcontent-%COMP%]{background-color:#00000017}.mat-tree-node[_ngcontent-%COMP%]{min-height:1.5rem;padding-bottom:.2rem;z-index:100000}.nodeIcon[_ngcontent-%COMP%]{height:13px;width:13px;margin-right:.5rem;font-family:FontAwesome}.rename-input[_ngcontent-%COMP%]{width:100%;background-color:#fff;border:1px solid #0069b4;padding:.2rem;transition:box-shadow .3s ease}.menu-link[_ngcontent-%COMP%]{background-color:#fff;border-radius:4px;font-size:13px;box-shadow:0 2px 10px #0003;position:absolute;z-index:1000;cursor:pointer}.menu-link[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;max-height:-moz-fit-content;max-height:fit-content;height:1rem;border:none;cursor:pointer}.menu-link[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:#f5f5f5}.menu-link[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:focus{outline:none;background-color:#e0e0e0}.drag-root[_ngcontent-%COMP%]{position:relative;width:100vw;height:100vh}.cdk-drag-preview[_ngcontent-%COMP%]{background-color:#0000001a;border-radius:4px}.cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.search-container[_ngcontent-%COMP%]{top:0;position:sticky}.search-results[_ngcontent-%COMP%]{max-height:276px;overflow-y:auto;font-size:13px}.search-result-item[_ngcontent-%COMP%]{cursor:pointer;height:32px!important;transition:background-color .2s}.search-result-item[_ngcontent-%COMP%]:hover{background-color:#0000000a}.search-result-item.selected[_ngcontent-%COMP%]{background-color:#00000014}.result-path[_ngcontent-%COMP%]{font-size:.85em;opacity:.7}.not-found-text[_ngcontent-%COMP%]{margin-block:.5rem;padding-inline:.7rem}.diagrams-list-view[_ngcontent-%COMP%]{padding:8px 0;margin-top:8px;max-height:calc(100vh - 200px);overflow-y:auto;font-size:14px}.diagrams-list-view[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{padding:0 16px;margin:0 0 8px;font-size:16px;font-weight:500;color:#000000de}.diagrams-list-view[_ngcontent-%COMP%]   .mat-list[_ngcontent-%COMP%]{padding:0}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:4px 16px;cursor:pointer;transition:background-color .2s ease;height:36px!important}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]:hover{background-color:#0000000a}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item.selected-diagram[_ngcontent-%COMP%]{background-color:#1976d21f;color:#1976d2}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]   .nodeIcon[_ngcontent-%COMP%]{margin-right:8px}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]   .nodeIcon[_ngcontent-%COMP%]     svg{width:20px;height:20px}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}  .diagrams-list-view .mat-list-item-content{padding:0!important}.selected-diagram[_ngcontent-%COMP%]{background-color:#1976d21f;color:#1976d2;font-weight:500}.drop-target[_ngcontent-%COMP%]{position:relative}.drop-target.can-drop[_ngcontent-%COMP%]{border-radius:4px;transition:all .2s ease}.drop-target.can-drop[_ngcontent-%COMP%]:after{content:"";position:absolute;inset:0;pointer-events:none}.drop-target.cannot-drop[_ngcontent-%COMP%]{position:relative}.drop-target.cannot-drop[_ngcontent-%COMP%]:after{content:"";position:absolute;inset:0;border-radius:4px;pointer-events:none}.dragging[_ngcontent-%COMP%]{cursor:move}']})}return n})();var Li=l(7200),Ri=l(9110),je=l(7155),Ne=l(6985),me=l(2651),oe=l(3305);const fe=[{name:"Primary colors",colors:[{key:"red",value:"rgba(229, 57, 53, 0.8)",friendlyName:"Red"},{key:"green",value:"rgba(51, 182, 121, 0.8)",friendlyName:"Green"},{key:"blue",value:"rgba(0, 105, 180, 0.8)",friendlyName:"Blue"},{key:"lightblue",value:"rgba(184, 242, 255, 0.8)",friendlyName:"Light Blue"},{key:"skyblue",value:"rgba(50, 186, 232, 0.8)",friendlyName:"Sky Blue"},{key:"yellow",value:"rgba(245, 231, 158, 0.8)",friendlyName:"Yellow"},{key:"darkgray",value:"rgba(64, 64, 64, 0.8)",friendlyName:"Dark Gray"},{key:"gray",value:"rgba(169, 169, 169, 0.8)",friendlyName:"Gray"},{key:"lightgray",value:"rgba(223, 223, 223, 0.8)",friendlyName:"Light Gray"}]},{name:"Extended colors",colors:[{key:"darkred",value:"rgba(206, 79, 76, 0.8)",friendlyName:"Dark Red"},{key:"lightpink",value:"rgba(246, 200, 200, 0.8)",friendlyName:"Light Pink"},{key:"pink",value:"rgba(229, 53, 119, 0.8)",friendlyName:"Pink"},{key:"darkgreen",value:"rgba(29, 102, 68, 0.8)",friendlyName:"Dark Green"},{key:"mintgreen",value:"rgba(133, 211, 175, 0.8)",friendlyName:"Mint Green"},{key:"brightgreen",value:"rgba(51, 182, 69, 0.8)",friendlyName:"Bright Green"},{key:"olive",value:"rgba(177, 182, 51, 0.8)",friendlyName:"Olive"},{key:"orange",value:"rgba(248, 149, 38, 0.8)",friendlyName:"Orange"},{key:"lightorange",value:"rgba(249, 240, 193, 0.8)",friendlyName:"Light Orange"},{key:"brown",value:"rgba(175, 130, 7, 0.8)",friendlyName:"Brown"},{key:"salmon",value:"rgba(246, 108, 38, 0.8)",friendlyName:"Salmon"},{key:"coral",value:"rgba(246, 149, 38, 0.8)",friendlyName:"Coral"},{key:"cyan",value:"rgba(27, 160, 255, 0.8)",friendlyName:"Cyan"},{key:"maroon",value:"rgba(101, 25, 23, 0.8)",friendlyName:"Maroon"},{key:"purple",value:"rgba(121, 51, 182, 0.8)",friendlyName:"Purple"}]}],Bi=fe.flatMap(n=>n.colors);var Hi=l(7474),Ji=l(4009),Ui=l(9710),Yi=l(5533),Gi=l(6479),Ki=l(7288);const qi=["saturationLightness"],zi=["hueSlider"],Qi=["alphaSlider"];function Wi(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",28),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(2);return e.KtG(a.selectColor(r.value))}),e.qZA()}if(2&n){const t=s.$implicit,i=e.oxw(2);e.Udp("background-color",t.value),e.ekj("selected",i.isSelected(t.value)),e.Q6J("title",i.getColorNameTranslation(t.key))}}function Xi(n,s){if(1&n&&(e.TgZ(0,"div",25)(1,"div",3),e._uU(2),e.qZA(),e.TgZ(3,"div",26),e.YNc(4,Wi,1,5,"div",27),e.qZA()()),2&n){const t=s.$implicit,i=e.oxw();e.xp6(2),e.Oqu(i.getGroupNameTranslation(t.name)),e.xp6(2),e.Q6J("ngForOf",t.colors)}}let $i=(()=>{class n{onColorSelected(t){this.isInitializing||this.customColorSelected.emit(t)}constructor(t,i,o){this.elementRef=t,this.renderer=i,this.translateService=o,this.colorUpdateTimeout=null,this.isInitializing=!0,this.selectedColor="",this.colorSelected=new e.vpe,this.customColorSelected=new e.vpe,this.customColor="rgba(255, 255, 255, 0.8)",this.isValidColor=!0,this.hsla={h:0,s:100,l:50,a:.8},this.hueColor="rgb(255, 0, 0)",this.slCursorTop=0,this.slCursorLeft=100,this.hueCursorLeft=0,this.alphaCursorLeft=80,this.alphaSliderBackground="",this.isDraggingSL=!1,this.isDraggingHue=!1,this.isDraggingAlpha=!1,this.colorGroups=fe}ngOnInit(){this.adjustLayout(),this.selectedColor?(this.customColor=this.selectedColor,this.parseColorToHSLA(this.selectedColor),this.updateColorPicker(),this.validateColor()):this.updateColorFromHSLA(),setTimeout(()=>{this.isInitializing=!1},300)}ngAfterViewInit(){this.renderer.listen("document","mousemove",t=>{this.isDraggingSL?this.onSaturationLightnessMouseMove(t):this.isDraggingHue?this.onHueMouseMove(t):this.isDraggingAlpha&&this.onAlphaMouseMove(t)}),this.renderer.listen("document","mouseup",()=>{this.isDraggingSL=!1,this.isDraggingHue=!1,this.isDraggingAlpha=!1})}onResize(){this.adjustLayout()}adjustLayout(){const t=this.elementRef.nativeElement.offsetWidth,i=this.elementRef.nativeElement.querySelector(".color-groups-container");i&&(t<180?i.classList.add("compact-layout"):i.classList.remove("compact-layout"))}selectColor(t){this.selectedColor=t,this.isInitializing||this.colorSelected.emit(t)}isSelected(t){const i=o=>o.toLowerCase().replace(/\s/g,"");return i(this.selectedColor)===i(t)}validateColor(){this.isValidColor=/^#([A-Fa-f0-9]{3}){1,2}$/.test(this.customColor)||/^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,?\s*[0-9\.]*\s*\)$/.test(this.customColor),this.isValidColor&&(this.parseColorToHSLA(this.customColor),this.updateColorPicker(),this.selectedColor=this.customColor,this.colorUpdateTimeout&&clearTimeout(this.colorUpdateTimeout),this.colorUpdateTimeout=setTimeout(()=>{this.isInitializing||this.customColorSelected.emit(this.customColor)},100))}onSaturationLightnessMouseDown(t){this.isDraggingSL=!0,this.onSaturationLightnessMouseMove(t)}onSaturationLightnessMouseMove(t){if(!this.isDraggingSL)return;const i=this.saturationLightnessRef.nativeElement.getBoundingClientRect(),o=Math.max(0,Math.min(100,(t.clientX-i.left)/i.width*100)),r=Math.max(0,Math.min(100,(t.clientY-i.top)/i.height*100));this.slCursorLeft=o,this.slCursorTop=r,this.hsla.s=o,this.hsla.l=100-r,this.updateColorFromHSLA()}onHueMouseDown(t){this.isDraggingHue=!0,this.onHueMouseMove(t)}onHueMouseMove(t){if(!this.isDraggingHue)return;const i=this.hueSliderRef.nativeElement.getBoundingClientRect(),o=Math.max(0,Math.min(100,(t.clientX-i.left)/i.width*100));this.hueCursorLeft=o,this.hsla.h=3.6*o,this.updateHueColor(),this.updateColorFromHSLA()}onAlphaMouseDown(t){this.isDraggingAlpha=!0,this.onAlphaMouseMove(t)}onAlphaMouseMove(t){if(!this.isDraggingAlpha)return;const i=this.alphaSliderRef.nativeElement.getBoundingClientRect(),o=Math.max(0,Math.min(100,(t.clientX-i.left)/i.width*100));this.alphaCursorLeft=o,this.hsla.a=o/100,this.updateColorFromHSLA()}parseColorToHSLA(t){let i=0,o=0,r=0,a=1;if(t.startsWith("#")){const h=t.substring(1),_=parseInt(h,16);i=_>>16&255,o=_>>8&255,r=255&_}else if(t.startsWith("rgba")){const h=t.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/);h&&(i=parseInt(h[1]),o=parseInt(h[2]),r=parseInt(h[3]),a=h[4]?parseFloat(h[4]):1)}else if(t.startsWith("rgb")){const h=t.match(/rgb?\((\d+),\s*(\d+),\s*(\d+)\)/);h&&(i=parseInt(h[1]),o=parseInt(h[2]),r=parseInt(h[3]))}i/=255,o/=255,r/=255;const c=Math.max(i,o,r),d=Math.min(i,o,r);let g=0,C=0,f=(c+d)/2;if(c!==d){const h=c-d;switch(C=f>.5?h/(2-c-d):h/(c+d),c){case i:g=(o-r)/h+(o<r?6:0);break;case o:g=(r-i)/h+2;break;case r:g=(i-o)/h+4}g/=6}this.hsla={h:Math.round(360*g),s:Math.round(100*C),l:Math.round(100*f),a},this.slCursorLeft=this.hsla.s,this.slCursorTop=100-this.hsla.l,this.hueCursorLeft=this.hsla.h/3.6,this.alphaCursorLeft=100*this.hsla.a}updateColorFromHSLA(){const t=this.hsla.h/360,i=this.hsla.s/100,o=this.hsla.l/100,r=this.hsla.a;let a,c,d;if(0===i)a=c=d=o;else{const C=(_,b,P)=>(P<0&&(P+=1),P>1&&(P-=1),P<.16666666666666666?_+6*(b-_)*P:P<.5?b:P<.6666666666666666?_+(b-_)*(.6666666666666666-P)*6:_),f=o<.5?o*(1+i):o+i-o*i,h=2*o-f;a=C(h,f,t+1/3),c=C(h,f,t),d=C(h,f,t-1/3)}const g_r=Math.round(255*a),g_g=Math.round(255*c),g_b=Math.round(255*d);this.customColor=`rgba(${g_r}, ${g_g}, ${g_b}, ${r})`,this.updateColorPicker(),this.isValidColor&&(this.selectedColor=this.customColor,this.colorUpdateTimeout&&clearTimeout(this.colorUpdateTimeout),this.colorUpdateTimeout=setTimeout(()=>{this.isInitializing||this.customColorSelected.emit(this.customColor)},100))}updateColorPicker(){this.updateHueColor();const t=this.customColor.replace(/,[^,]+\)/,",1)");this.alphaSliderBackground=`linear-gradient(to right, rgba(0, 0, 0, 0), ${t})`}updateHueColor(){this.hueColor=`hsl(${this.hsla.h}, 100%, 50%)`}getGroupNameTranslation(t){return"Primary colors"===t?this.translateService.instant("colorPicker.primaryColors"):"Extended colors"===t?this.translateService.instant("colorPicker.extendedColors"):t}getColorNameTranslation(t){const o=`colorPicker.colors.${t.replace(/(^|\s)\w/g,a=>a.toUpperCase()).replace(/\s+/g,"").replace(/^\w/,a=>a.toLowerCase())}`,r=this.translateService.instant(o);return r!==o?r:t}ngOnDestroy(){this.colorUpdateTimeout&&clearTimeout(this.colorUpdateTimeout)}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(e.SBq),e.Y36(e.Qsj),e.Y36(S.sK))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-color-groups"]],viewQuery:function(i,o){if(1&i&&(e.Gf(qi,5),e.Gf(zi,5),e.Gf(Qi,5)),2&i){let r;e.iGM(r=e.CRH())&&(o.saturationLightnessRef=r.first),e.iGM(r=e.CRH())&&(o.hueSliderRef=r.first),e.iGM(r=e.CRH())&&(o.alphaSliderRef=r.first)}},hostBindings:function(i,o){1&i&&e.NdJ("resize",function(){return o.onResize()},!1,e.Jf7)},inputs:{selectedColor:"selectedColor"},outputs:{colorSelected:"colorSelected",customColorSelected:"customColorSelected"},decls:28,vars:29,consts:[[1,"color-groups-container"],["class","color-group",4,"ngFor","ngForOf"],[1,"color-group","custom-color-group"],[1,"group-label"],[1,"custom-color-section"],[1,"saturation-lightness-container",3,"mousedown"],["saturationLightness",""],[1,"saturation-lightness"],[1,"saturation-lightness-overlay-1"],[1,"saturation-lightness-overlay-2"],[1,"saturation-lightness-cursor"],[1,"saturation-lightness-cursor-inner"],[1,"sliders-preview-container"],[1,"custom-color-preview",3,"title"],[1,"sliders-container"],[1,"hue-container",3,"mousedown"],["hueSlider",""],[1,"hue-slider"],[1,"hue-cursor"],[1,"hue-cursor-inner"],[1,"alpha-container",3,"mousedown"],["alphaSlider",""],[1,"alpha-slider"],[1,"alpha-cursor"],[1,"alpha-cursor-inner"],[1,"color-group"],[1,"color-swatches"],["class","color-swatch",3,"background-color","selected","title","click",4,"ngFor","ngForOf"],[1,"color-swatch",3,"title","click"]],template:function(i,o){1&i&&(e.TgZ(0,"div",0),e.YNc(1,Xi,5,2,"div",1),e.TgZ(2,"div",2)(3,"div",3),e._uU(4),e.ALo(5,"translate"),e.qZA(),e.TgZ(6,"div",4)(7,"div",5,6),e.NdJ("mousedown",function(a){return o.onSaturationLightnessMouseDown(a)}),e._UZ(9,"div",7)(10,"div",8)(11,"div",9),e.TgZ(12,"div",10),e._UZ(13,"div",11),e.qZA()(),e.TgZ(14,"div",12),e._UZ(15,"div",13),e.ALo(16,"translate"),e.TgZ(17,"div",14)(18,"div",15,16),e.NdJ("mousedown",function(a){return o.onHueMouseDown(a)}),e._UZ(20,"div",17),e.TgZ(21,"div",18),e._UZ(22,"div",19),e.qZA()(),e.TgZ(23,"div",20,21),e.NdJ("mousedown",function(a){return o.onAlphaMouseDown(a)}),e._UZ(25,"div",22),e.TgZ(26,"div",23),e._UZ(27,"div",24),e.qZA()()()()()()()),2&i&&(e.xp6(1),e.Q6J("ngForOf",o.colorGroups),e.xp6(3),e.Oqu(e.lcZ(5,25,"colorPicker.customColor")),e.xp6(5),e.Udp("background-color",o.hueColor),e.xp6(3),e.Udp("top",o.slCursorTop,"%")("left",o.slCursorLeft,"%"),e.xp6(1),e.Udp("background-color",o.customColor),e.xp6(2),e.Udp("background-color",o.customColor),e.ekj("selected",o.isSelected(o.customColor)),e.Q6J("title",e.lcZ(16,27,o.customColor)),e.xp6(6),e.Udp("left",o.hueCursorLeft,"%"),e.xp6(1),e.Udp("background-color",o.hueColor),e.xp6(3),e.Udp("background-image",o.alphaSliderBackground),e.xp6(1),e.Udp("left",o.alphaCursorLeft,"%"),e.xp6(1),e.Udp("background-color",o.customColor))},dependencies:[v.sg,S.X$],styles:['@charset "UTF-8";.color-groups-container[_ngcontent-%COMP%]{padding:6px 8px;max-height:220px;overflow-y:auto;width:100%;box-sizing:border-box;min-width:0;scrollbar-width:thin}.color-groups-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.color-groups-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}.color-groups-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}.color-groups-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}.color-groups-container.compact-layout[_ngcontent-%COMP%]{padding:8px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .color-group[_ngcontent-%COMP%]{margin-bottom:10px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .color-group[_ngcontent-%COMP%]:not(:last-child):after{bottom:-5px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .group-label[_ngcontent-%COMP%]{font-size:11px;margin-bottom:6px;padding-bottom:3px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .group-label[_ngcontent-%COMP%]:after{height:1px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .color-swatches[_ngcontent-%COMP%]{gap:4px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .color-swatch[_ngcontent-%COMP%]{width:14px;height:14px;border-radius:2px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .color-swatch.selected[_ngcontent-%COMP%]{box-shadow:0 0 0 1px #fff,0 0 0 1px var(--primary-color)}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .color-swatch.selected[_ngcontent-%COMP%]:before{border-radius:1px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .color-swatch.selected[_ngcontent-%COMP%]:after{font-size:8px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .custom-color-group[_ngcontent-%COMP%]{padding:6px;margin-bottom:8px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .custom-color-section[_ngcontent-%COMP%]{gap:4px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .sliders-preview-container[_ngcontent-%COMP%]{gap:6px;flex-direction:column;align-items:flex-start}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .custom-color-preview[_ngcontent-%COMP%]{width:24px;height:24px;border-width:1px;margin-bottom:4px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .sliders-container[_ngcontent-%COMP%]{width:100%;gap:6px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .saturation-lightness-container[_ngcontent-%COMP%]{height:80px;border-width:1px;border-radius:6px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .saturation-lightness-cursor[_ngcontent-%COMP%]{width:8px;height:8px;transform:translate(-4px,-4px);border-width:1px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .hue-container[_ngcontent-%COMP%], .color-groups-container.compact-layout[_ngcontent-%COMP%]   .alpha-container[_ngcontent-%COMP%]{height:6px;border-width:1px;border-radius:3px}.color-groups-container.compact-layout[_ngcontent-%COMP%]   .hue-cursor[_ngcontent-%COMP%], .color-groups-container.compact-layout[_ngcontent-%COMP%]   .alpha-cursor[_ngcontent-%COMP%]{width:6px;height:6px;transform:translate(-3px);border-width:1px}.color-group[_ngcontent-%COMP%]{margin-bottom:10px;position:relative}.color-group.custom-color-group[_ngcontent-%COMP%]{padding:8px 10px 12px;border-radius:8px;background:linear-gradient(145deg,#f8f9fa,#fff);box-shadow:0 3px 10px #0000000d;margin-top:15px;margin-bottom:0;border:1px solid rgba(0,0,0,.05);overflow:visible;position:relative}.color-group.custom-color-group[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient(to right,var(--primary-color),rgba(10,93,168,.2));border-radius:8px 8px 0 0}.color-group.custom-color-group[_ngcontent-%COMP%]   .group-label[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:var(--primary-color);margin-bottom:8px}.color-group.custom-color-group[_ngcontent-%COMP%]   .group-label[_ngcontent-%COMP%]:after{background:linear-gradient(to right,var(--primary-color),rgba(10,93,168,.2));height:2px;width:30px}.color-group[_ngcontent-%COMP%]:last-child{margin-bottom:0}.color-group[_ngcontent-%COMP%]:not(.custom-color-group):not(:last-child):after{content:"";position:absolute;bottom:-8px;left:0;right:0;height:1px;background:linear-gradient(to right,transparent,rgba(0,0,0,.08),transparent)}.group-label[_ngcontent-%COMP%]{font-weight:600;margin-bottom:4px;font-size:10px;color:#333;position:relative;display:inline-block;padding-bottom:2px}.group-label[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:0;width:100%;height:2px;background:linear-gradient(to right,var(--primary-color),transparent);border-radius:2px}.color-swatches[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:3px;justify-content:flex-start;padding:2px;width:100%}.color-swatch[_ngcontent-%COMP%]{width:16px;height:16px;border-radius:3px;cursor:pointer;transition:all .2s ease;border:1px solid rgba(0,0,0,.08);flex:0 0 auto;position:relative;box-shadow:0 1px 2px #0000000d}.color-swatch[_ngcontent-%COMP%]:hover{transform:translateY(-1px) scale(1.15);box-shadow:0 2px 6px #00000026;z-index:1}.color-swatch[_ngcontent-%COMP%]:active{transform:translateY(1px);box-shadow:0 1px 2px #0000001a}.color-swatch.selected[_ngcontent-%COMP%]{box-shadow:0 0 0 1px #fff,0 0 0 2px var(--primary-color);transform:scale(1.1);z-index:2}.color-swatch.selected[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;border-radius:3px;box-shadow:inset 0 0 0 1px #ffffff4d;pointer-events:none}.color-swatch.selected[_ngcontent-%COMP%]:after{content:"\\2713";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#fff;font-size:10px;font-weight:700;text-shadow:0 0 2px rgba(0,0,0,.8)}.custom-color-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:6px;padding-bottom:0;animation:fadeIn .3s ease-out}.custom-color-section[_ngcontent-%COMP%]   .saturation-lightness-container[_ngcontent-%COMP%]{height:120px}.sliders-preview-container[_ngcontent-%COMP%]{display:flex;gap:10px;align-items:center;margin-top:10px}.sliders-preview-container[_ngcontent-%COMP%]   .sliders-container[_ngcontent-%COMP%]{min-width:0;display:flex;flex-direction:column;gap:8px;flex:1}.custom-color-preview[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:2px solid white;position:relative;cursor:pointer;flex-shrink:0;box-shadow:0 2px 6px #0000001f;transition:all .2s ease;margin-right:10px}.custom-color-preview[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 3px 10px #0003}.custom-color-preview[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background-image:linear-gradient(45deg,#f0f0f0 25%,transparent 25%),linear-gradient(-45deg,#f0f0f0 25%,transparent 25%),linear-gradient(45deg,transparent 75%,#f0f0f0 75%),linear-gradient(-45deg,transparent 75%,#f0f0f0 75%);background-size:6px 6px;background-position:0 0,0 3px,3px -3px,-3px 0px;border-radius:3px;z-index:-1}.custom-color-preview.selected[_ngcontent-%COMP%]{box-shadow:0 0 0 2px #fff,0 0 0 4px var(--primary-color);transform:scale(1.05)}.custom-color-preview.selected[_ngcontent-%COMP%]:after{content:"\\2713";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#fff;font-size:16px;font-weight:700;text-shadow:0 0 3px rgba(0,0,0,.8)}.saturation-lightness-container[_ngcontent-%COMP%]{position:relative;width:100%;height:120px;border-radius:8px;overflow:hidden;cursor:crosshair;-webkit-user-select:none;user-select:none;box-shadow:0 2px 6px #00000014;border:1px solid white;transition:all .2s ease}.saturation-lightness-container[_ngcontent-%COMP%]:hover{box-shadow:0 5px 15px #00000026}.saturation-lightness[_ngcontent-%COMP%]{position:absolute;inset:0}.saturation-lightness-overlay-1[_ngcontent-%COMP%]{position:absolute;inset:0;background:linear-gradient(to right,white,rgba(255,255,255,0))}.saturation-lightness-overlay-2[_ngcontent-%COMP%]{position:absolute;inset:0;background:linear-gradient(to bottom,rgba(0,0,0,0),black)}.saturation-lightness-cursor[_ngcontent-%COMP%]{position:absolute;width:14px;height:14px;transform:translate(-7px,-7px);border-radius:50%;border:2px solid white;box-shadow:0 0 3px #0009;pointer-events:none;transition:transform .1s ease}.saturation-lightness-cursor-inner[_ngcontent-%COMP%], .hue-cursor-inner[_ngcontent-%COMP%], .alpha-cursor-inner[_ngcontent-%COMP%]{position:absolute;inset:0;border-radius:50%;border:.5px solid rgba(0,0,0,.3)}.hue-container[_ngcontent-%COMP%]{position:relative;width:100%;height:10px;border-radius:5px;overflow:hidden;cursor:pointer;-webkit-user-select:none;user-select:none;box-shadow:0 1px 3px #00000014;border:1px solid white}.hue-slider[_ngcontent-%COMP%]{position:absolute;inset:0;background:linear-gradient(to right,#ff0000,#ffff00,#00ff00,#00ffff,#0000ff,#ff00ff,#ff0000)}.hue-cursor[_ngcontent-%COMP%]{position:absolute;width:10px;height:10px;transform:translate(-5px);pointer-events:none;transition:all .1s ease;border-radius:50%;border:1.5px solid white;box-shadow:0 0 2px #00000080}.alpha-container[_ngcontent-%COMP%]{position:relative;width:100%;height:10px;border-radius:5px;overflow:hidden;cursor:pointer;-webkit-user-select:none;user-select:none;box-shadow:0 1px 3px #00000014;border:1px solid white;background-image:linear-gradient(45deg,#ccc 25%,transparent 25%),linear-gradient(-45deg,#ccc 25%,transparent 25%),linear-gradient(45deg,transparent 75%,#ccc 75%),linear-gradient(-45deg,transparent 75%,#ccc 75%);background-size:6px 6px;background-position:0 0,0 3px,3px -3px,-3px 0px}.alpha-slider[_ngcontent-%COMP%]{position:absolute;inset:0}.alpha-cursor[_ngcontent-%COMP%]{position:absolute;width:10px;height:10px;transform:translate(-5px);pointer-events:none;transition:all .1s ease;border-radius:50%;border:1.5px solid white;box-shadow:0 0 2px #00000080}.custom-color-input[_ngcontent-%COMP%]{flex:1;min-width:0;padding:8px 12px;border:1px solid rgba(0,0,0,.15);border-radius:20px;font-size:12px;height:40px;background-color:#fff;color:#333;transition:all .2s ease;font-family:Roboto,sans-serif;letter-spacing:.3px;text-overflow:ellipsis}.custom-color-input[_ngcontent-%COMP%]::placeholder{color:#999;font-style:italic;font-size:11px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.custom-color-input[_ngcontent-%COMP%]:focus{outline:none;border-color:var(--primary-color);box-shadow:0 0 0 2px #0a5da833;background-color:#fafafa}']})}return n})();function eo(n,s){if(1&n&&(e.TgZ(0,"mat-option",15),e._uU(1),e.qZA()),2&n){const t=s.$implicit;e.Q6J("value",t.id),e.xp6(1),e.hij("",t.name," ")}}function to(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",5)(1,"mat-form-field",6)(2,"mat-select",13),e.NdJ("selectionChange",function(o){e.CHM(t);const r=e.oxw(2);return e.KtG(r.onSelectionChange(o))})("valueChange",function(o){e.CHM(t);const r=e.oxw(2);return e.KtG(r.form.value.dataType=o)}),e.YNc(3,eo,2,2,"mat-option",14),e.qZA()()()}if(2&n){const t=e.oxw(2);e.xp6(2),e.Q6J("value",t.form.value.dataType),e.xp6(1),e.Q6J("ngForOf",t.attributeTypes)}}function io(n,s){1&n&&(e.TgZ(0,"div",16)(1,"mat-form-field",17)(2,"mat-label"),e._uU(3),e.ALo(4,"translate"),e.qZA(),e._UZ(5,"textarea",18),e.qZA()()),2&n&&(e.xp6(3),e.Oqu(e.lcZ(4,1,"property.description")))}function oo(n,s){if(1&n){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",5)(2,"mat-form-field",6)(3,"mat-label"),e._uU(4),e.ALo(5,"translate"),e.qZA(),e.TgZ(6,"input",19),e.NdJ("value",function(){e.CHM(t);const o=e.oxw(2);return e.KtG(o.form.value.volumetry)}),e.qZA()()(),e.TgZ(7,"div",5)(8,"mat-form-field",6)(9,"mat-label"),e._uU(10),e.ALo(11,"translate"),e.qZA(),e.TgZ(12,"input",20),e.NdJ("value",function(){e.CHM(t);const o=e.oxw(2);return e.KtG(o.form.value.tag)}),e.qZA()()(),e.BQk()}2&n&&(e.xp6(4),e.Oqu(e.lcZ(5,2,"property.volumetry")),e.xp6(6),e.Oqu(e.lcZ(11,4,"property.tag")))}function no(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",5)(1,"mat-form-field",6)(2,"mat-label"),e._uU(3),e.ALo(4,"translate"),e.qZA(),e.TgZ(5,"input",21),e.NdJ("value",function(){e.CHM(t);const o=e.oxw(2);return e.KtG(o.form.value.fromComment)}),e.qZA()()()}2&n&&(e.xp6(3),e.Oqu(e.lcZ(4,1,"property.fromComment")))}function ro(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",5)(1,"mat-form-field",6)(2,"mat-label"),e._uU(3),e.ALo(4,"translate"),e.qZA(),e.TgZ(5,"input",22),e.NdJ("value",function(){e.CHM(t);const o=e.oxw(2);return e.KtG(o.form.value.toComment)}),e.qZA()()()}2&n&&(e.xp6(3),e.Oqu(e.lcZ(4,1,"property.toComment")))}function ao(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",28)(1,"div",29)(2,"span"),e._uU(3),e.ALo(4,"translate"),e.qZA(),e.TgZ(5,"button",30),e.NdJ("click",function(){e.CHM(t);const o=e.oxw(3);return e.KtG(o.toggleColorPicker())}),e._uU(6," \xd7 "),e.qZA()(),e.TgZ(7,"app-color-groups",31),e.NdJ("colorSelected",function(o){e.CHM(t);const r=e.oxw(3);return e.KtG(r.onColorSelected(o))})("customColorSelected",function(o){e.CHM(t);const r=e.oxw(3);return e.KtG(r.onCustomColorSelected(o))}),e.qZA()()}if(2&n){const t=e.oxw(3);e.xp6(3),e.Oqu(e.lcZ(4,2,"colorPicker.selectColor")),e.xp6(4),e.Q6J("selectedColor",t.color)}}function so(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",5)(1,"mat-form-field",23)(2,"mat-label"),e._uU(3),e.ALo(4,"translate"),e.qZA(),e.TgZ(5,"div",24),e._UZ(6,"input",25),e.TgZ(7,"div",26),e.NdJ("click",function(){e.CHM(t);const o=e.oxw(2);return e.KtG(o.toggleColorPicker())}),e.qZA()()(),e.YNc(8,ao,8,4,"div",27),e.qZA()}if(2&n){const t=e.oxw(2);e.xp6(3),e.Oqu(e.lcZ(4,5,"property.color")),e.xp6(3),e.Q6J("value",t.color),e.xp6(1),e.Udp("background-color",t.color),e.xp6(1),e.Q6J("ngIf",t.showColorPicker)}}function co(n,s){1&n&&(e.TgZ(0,"p"),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&n&&(e.xp6(1),e.Oqu(e.lcZ(2,1,"diagram.propertyDescription")))}function lo(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",1)(1,"span",2),e._uU(2),e.ALo(3,"translate"),e.qZA(),e.TgZ(4,"form",3)(5,"div",4)(6,"div",5)(7,"mat-form-field",6)(8,"mat-label"),e._uU(9),e.ALo(10,"translate"),e.qZA(),e.TgZ(11,"input",7,8),e.NdJ("value",function(){e.CHM(t);const o=e.oxw();return e.KtG(o.form.value.name)})("click",function(){e.CHM(t);const o=e.MAs(12);return e.KtG(o.select())}),e.qZA()()(),e.YNc(13,to,4,2,"div",9),e.YNc(14,io,6,3,"div",10),e.YNc(15,oo,13,6,"ng-container",11),e.YNc(16,no,6,3,"div",9),e.YNc(17,ro,6,3,"div",9),e.YNc(18,so,9,7,"div",9),e.qZA()(),e.YNc(19,co,3,3,"ng-template",null,12,e.W1O),e.qZA()}if(2&n){const t=e.oxw();e.xp6(2),e.Oqu(e.lcZ(3,9,"diagram.properties")),e.xp6(2),e.Q6J("formGroup",t.form),e.xp6(5),e.Oqu(e.lcZ(10,11,"property.name")),e.xp6(4),e.Q6J("ngIf",t.form.value.dataType),e.xp6(1),e.Q6J("ngIf",t.isGroupNode||t.isAttributeNode),e.xp6(1),e.Q6J("ngIf",t.isGroupNode),e.xp6(1),e.Q6J("ngIf",t.isLinkNode),e.xp6(1),e.Q6J("ngIf",t.isLinkNode),e.xp6(1),e.Q6J("ngIf",t.isGroupNode||t.isLinkNode)}}let po=(()=>{class n{constructor(t,i,o,r,a,c,d,g,C,f,h,_){this.fb=t,this.attributeService=i,this.enumerationService=o,this.folderService=r,this.classService=a,this.literalService=c,this.propertyService=d,this.commonGojsService=g,this.linkService=C,this.diagramUtils=f,this.elementRef=h,this.translateService=_,this.isGroupNode=!1,this.isAttributeNode=!1,this.showPropertyForm=!1,this.isLinkNode=!1,this.showColorPicker=!1,this.cardinalities=["0..1","1","*","1..*"],this.inputSubject=new z.x,this.destroy$=new z.x,this.debounceTimeMs=400,this.presetValues=[],this.colorGroupsConfig={},this.haveEditAccess=!1,this.currentDiagramId=0,this.updateNodePropertyEmitter=new e.vpe,this.colorPickerSelectEmitter=new e.vpe,this.attributeTypes=[],this.presetValues=this.getColorValues(),this.colorGroupsConfig=this.getColorGroups()}ngOnInit(){this.inputSubject.pipe((0,se.b)(this.debounceTimeMs),(0,I.R)(this.destroy$)).subscribe(t=>{""!=t.name.trim()&&(this.nodeProperty={...this.nodeProperty,...t},t.category==p.J.Class||t.category==p.J.AssociativeClass?this.commonGojsService.isGojsDiagramClassNode(this.nodeProperty)&&this.updateTemplateClass(this.nodeProperty):t.category==p.J.Enumeration?this.commonGojsService.isGojsDiagramEnumerationNode(this.nodeProperty)&&this.updateTemplateEnumeration(this.nodeProperty):t.category==p.J.Attribute||t.category==p.J.Operation?this.commonGojsService.isGojsDiagramAttributeNode(this.nodeProperty)&&this.updateAttribute(this.nodeProperty):t.category==p.J.EnumerationLiteral?this.commonGojsService.isGojsDiagramLiteralNode(this.nodeProperty)&&this.updateLiteral(this.nodeProperty):this.nodeProperty.category==p.J.Folder?this.commonGojsService.isGojsPaletteFolderNode(this.nodeProperty)&&this.updateFolder({id:this.nodeProperty.idFolder,name:t.name,icon:this.nodeProperty.icon}):this.nodeProperty.category==p.J.Association&&this.updateLinkNode({...this.nodeProperty,name:t.name,color:this.nodeProperty.color,cardinalityFrom:this.nodeProperty.cardinalityFrom,cardinalityTo:this.nodeProperty.cardinalityTo,segmentOffset:this.nodeProperty.segmentOffset}))}),this.getPropertyData()}updateLiteral(t){this.literalService.updateEnumerationLiteral({id:t.id,name:t.name,key:t.key}).subscribe(i=>{this.updateNodePropertyEmitter.emit({...t,...i})})}updateTemplateEnumeration(t){this.enumerationService.updateTempEnumeration({id:t.idTemplateEnumeration,name:t.name,key:this.nodeProperty.key,icon:t.icon,color:t.color,description:t.description,volumetry:t.volumetry,tag:t.tag},this.currentDiagramId).subscribe(i=>{this.updateNodePropertyEmitter.emit({...this.nodeProperty,...i}),this.diagramUtils.updateAttributeType(i.id?.toString(),i.name)})}updateTemplateClass(t){this.classService.updateTemplateClass({id:t.idTemplateClass,name:t.name,key:t?.key?.toString().split("_")[0],icon:t.icon,color:t.color,tag:t?.tag,volumetry:t?.volumetry,description:t?.description,isAssociative:t.category==p.J.AssociativeClass},this.currentDiagramId).subscribe(i=>{this.updateNodePropertyEmitter.emit({...t,id:t.idTemplateClass})})}updateAttribute(t){const i=this.attributeTypes.find(o=>o.id===t.dataType);this.attributeService.updateAttribute({id:t.id,name:t.name,description:t?.description,category:t.memberType,attributeType:i?.isEnumeration?L.G.Undefined:this.attributeService.getDefaultAttributeId(t.dataType.toString()),idTemplateEnumeration:i?.isEnumeration?i?.id:0,key:t.key}).subscribe(o=>{this.updateNodePropertyEmitter.emit({...t,dataType:0==o.idTemplateEnumeration?`${o.attributeType}_${L.G[o.attributeType]}`:o.idTemplateEnumeration?.toString()})})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}updateFolder(t){this.folderService.updateFolder(t).subscribe(i=>{this.updateNodePropertyEmitter.emit({...this.nodeProperty,...i})})}updateLinkNode(t){this.linkService.updateLink({idLinkType:t.idLinkType,color:t.color,id:+t?.key,name:t.name,fromComment:t.fromComment,toComment:t.toComment,idSourceTempClass:t.idSourceTempClass,idDestinationTempClass:t.idDestinationTempClass,segmentOffset:t.segmentOffset,linkPort:{idDiagram:this.currentDiagramId,destinationPort:t.toPort,sourcePort:t.fromPort,idLink:t.id,segmentOffset:t.segmentOffset}}).subscribe(i=>{this.updateNodePropertyEmitter.emit({...this.nodeProperty,...i})})}getPropertyData(){this.propertyService.propertyDataChanges().pipe((0,I.R)(this.destroy$)).subscribe(t=>{t&&Object.keys(t).length>0?(this.showPropertyForm=!0,this.nodeProperty=t,this.isLinkNode=this.nodeProperty.category==p.J.Association,this.isAttributeNode=this.nodeProperty.category===p.J.Attribute||this.nodeProperty.category===p.J.Operation,this.isGroupNode=this.nodeProperty.category===p.J.Enumeration||this.nodeProperty.category===p.J.Class||this.nodeProperty.category===p.J.AssociativeClass,this.initForm(t)):this.showPropertyForm=!1})}initForm(t){const{category:i,name:o,description:r,volumetry:a,tag:c,color:d,dataType:g,fromComment:C,toComment:f}=t;if(i===p.J.Operation||i===p.J.Attribute){const _=this.attributeTypes.find(b=>b.id===g.toString());t.dataType=_?_.id:`0_${L.G[L.G.Undefined]}`}const h=_=>new m.NI({value:_,disabled:!this.haveEditAccess});this.form=this.fb.group({name:h(o),description:h(r),volumetry:h(a),tag:h(c),color:h(d),dataType:h(g?.toString()),category:h(i),fromComment:h(C),toComment:h(f)}),this.color=d,this.form.valueChanges.pipe((0,I.R)(this.destroy$)).subscribe(_=>{this.inputSubject.next(_)})}changeColor(t){this.color=t,this.form.controls.color.setValue(t,{emitEvent:!0})}onSelectionChange(t){this.form.controls.dataType.setValue(t.value)}toggleColorPicker(){this.showColorPicker=!this.showColorPicker,this.colorPickerSelectEmitter.emit(this.showColorPicker),this.showColorPicker&&!this.color&&(this.color="rgba(229, 57, 53, 0.8)",this.form.controls.color.setValue(this.color,{emitEvent:!1}))}onColorSelected(t){this.color=t,this.changeColor(t),this.showColorPicker=!1,this.form.controls.color.setValue(t,{emitEvent:!1})}onCustomColorSelected(t){this.color=t,this.changeColor(t),this.form.controls.color.setValue(t,{emitEvent:!1})}handleDocumentClick(t){this.showColorPicker&&(this.elementRef.nativeElement.contains(t.target)||(this.showColorPicker=!1))}getColorValues(){return Bi.map(t=>t.value)}getColorGroups(){const t={};return fe.forEach(i=>{t[i.name]=i.colors.map(o=>o.value)}),t}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(m.qu),e.Y36(Hi.c),e.Y36(Ji.T),e.Y36(Ui.s),e.Y36(Yi.j),e.Y36(Gi.z),e.Y36(te.b),e.Y36(Ki.K),e.Y36(ee.Q),e.Y36(Z.T),e.Y36(e.SBq),e.Y36(S.sK))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-properties"]],hostBindings:function(i,o){1&i&&e.NdJ("click",function(a){return o.handleDocumentClick(a)},!1,e.evT)},inputs:{nodeProperty:["nodeData","nodeProperty"],haveEditAccess:["editAccess","haveEditAccess"],currentDiagramId:["idDiagram","currentDiagramId"],attributeTypes:["dataTypes","attributeTypes"]},outputs:{updateNodePropertyEmitter:"onChangePropertyData",colorPickerSelectEmitter:"isColorPickerSelected"},decls:1,vars:1,consts:[["class","properties",4,"ngIf"],[1,"properties"],[1,"property-title"],[1,"property-form",3,"formGroup"],[2,"position","relative"],[1,"property-field"],["appearance","outline"],["type","text","matInput","","name","nodeName","formControlName","name",3,"value","click"],["nameInput",""],["class","property-field",4,"ngIf"],["class","description-textArea",4,"ngIf"],[4,"ngIf"],["noProperty",""],[3,"value","selectionChange","valueChange"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],[1,"description-textArea"],["appearance","outline",1,"description-field"],["matInput","","formControlName","description","rows","2"],["type","text","matInput","","formControlName","volumetry",3,"value"],["type","text","matInput","","formControlName","tag",3,"value"],["type","text","matInput","","formControlName","fromComment",3,"value"],["type","text","matInput","","formControlName","toComment",3,"value"],["appearance","outline",1,"color-field-form"],[1,"color-field-container"],["matInput","","formControlName","color","readonly","true",2,"display","none",3,"value"],[1,"color-display",3,"click"],["class","color-picker-popup",4,"ngIf"],[1,"color-picker-popup"],[1,"color-picker-header"],[1,"close-button",3,"click"],[3,"selectedColor","colorSelected","customColorSelected"]],template:function(i,o){1&i&&e.YNc(0,lo,21,13,"div",0),2&i&&e.Q6J("ngIf",o.showPropertyForm)},dependencies:[v.sg,v.O5,D.KE,D.hX,le.Nt,U.ey,X.gD,m._Y,m.Fj,m.JJ,m.JL,m.sg,m.u,$i,S.X$],styles:['.properties[_ngcontent-%COMP%]{margin-top:1rem;margin-inline:4%;align-items:center}.properties[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%]{display:block;margin-bottom:1rem}.property-form[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper{height:1.5vh}.property-form[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:16px}.property-form[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(-23.75px) scale(var(--mat-mdc-form-field-floating-label-scale, .75));transform:var(--mat-mdc-form-field-label-transform)}.property-form[_ngcontent-%COMP%]     .mat-mdc-form-field-infix{margin-top:.2rem}.property-form[_ngcontent-%COMP%]   .link-port[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;align-items:center}.property-form[_ngcontent-%COMP%]   .link-port[_ngcontent-%COMP%] > mat-form-field[_ngcontent-%COMP%]:first-child{margin-right:.6rem}.property-field[_ngcontent-%COMP%]{display:flex!important;flex-direction:column;justify-content:center;margin-inline:.5rem;font-size:14px;position:relative}.property-field.color-field[_ngcontent-%COMP%]{position:relative}.property-field.color-field[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%}.property-field[_ngcontent-%COMP%]   mat-form-field.color-field-form[_ngcontent-%COMP%]   .mat-mdc-form-field-subscript-wrapper[_ngcontent-%COMP%]{display:none}.property-field[_ngcontent-%COMP%]   mat-form-field.color-field-form[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%]{padding-top:6px;padding-bottom:6px;display:flex}.property-field[_ngcontent-%COMP%]   mat-form-field.color-field-form[_ngcontent-%COMP%]   .mat-mdc-form-field-flex[_ngcontent-%COMP%]{align-items:center}.property-label[_ngcontent-%COMP%]{font-size:smaller;margin-bottom:.4rem;font-weight:400}.description-textArea[_ngcontent-%COMP%]{margin-inline:.5rem;font-size:14px}.description-field[_ngcontent-%COMP%]{width:100%;margin:0;font-size:14px;padding:0}.color-field-container[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;width:100%;height:32px;margin-top:4px}.color-display[_ngcontent-%COMP%]{cursor:pointer;height:20px;width:100%;border-radius:3px;position:relative;border:1px solid rgba(0,0,0,.12);margin-bottom:.4rem}.color-display[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background-image:linear-gradient(45deg,#f0f0f0 25%,transparent 25%),linear-gradient(-45deg,#f0f0f0 25%,transparent 25%),linear-gradient(45deg,transparent 75%,#f0f0f0 75%),linear-gradient(-45deg,transparent 75%,#f0f0f0 75%);background-size:6px 6px;background-position:0 0,0 3px,3px -3px,-3px 0px;border-radius:2px;z-index:-1}.color-picker-popup[_ngcontent-%COMP%]{position:absolute;bottom:100%;left:0;z-index:1000;background-color:#fff;border-radius:4px;box-shadow:0 2px 10px #0003;margin-bottom:8px;width:auto;min-width:200px;max-width:100%;border:1px solid rgba(0,0,0,.1);overflow:hidden}.color-picker-popup[_ngcontent-%COMP%]   .color-picker-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px 12px;border-bottom:1px solid rgba(0,0,0,.1);background-color:#f5f5f5;height:36px;box-sizing:border-box}.color-picker-popup[_ngcontent-%COMP%]   .color-picker-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:14px;color:#333}.color-picker-popup[_ngcontent-%COMP%]   .color-picker-header[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{background:none;border:none;font-size:18px;line-height:1;padding:4px 6px;cursor:pointer;color:#666}.mat-mdc-text-field-wrapper[_ngcontent-%COMP%]   .mat-mdc-form-field-flex[_ngcontent-%COMP%]   .mat-mdc-floating-label[_ngcontent-%COMP%]{top:16px}.mat-mdc-text-field-wrapper.mdc-text-field--outlined[_ngcontent-%COMP%]   .mdc-notched-outline--upgraded[_ngcontent-%COMP%]   .mdc-floating-label--float-above[_ngcontent-%COMP%]{--mat-mdc-form-field-label-transform: translateY(-23.75px) scale(var(--mat-mdc-form-field-floating-label-scale, .75));transform:var(--mat-mdc-form-field-label-transform)}.mat-mdc-form-field-infix[_ngcontent-%COMP%]{margin-top:.2rem}']})}return n})();const ho=function(n){return["fal",n]};let go=(()=>{class n{constructor(){this.isGridLayout=!1}ngOnInit(){this.diagram.grid.visible=this.isGridLayout}zoomIn(){if(null!=this.diagram){const i=Math.min(1.1*this.diagram.scale,2.5);this.diagram.scale=i}}zoomOut(){if(null!=this.diagram){const i=Math.max(this.diagram.scale/1.1,.25);this.diagram.scale=i}}changeDiagramLayout(){this.isGridLayout=!this.isGridLayout,this.diagram.grid.visible=this.isGridLayout}static#e=this.\u0275fac=function(i){return new(i||n)};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-action-controls"]],inputs:{diagram:"diagram"},decls:14,vars:14,consts:[[1,"grid-controls"],[3,"click"],["size","xl","matTooltipClass","tooltip-custom",1,"main-toolbar-icon",3,"icon","matTooltip"],[1,"zoom-controls"],[1,"topbar-icon",3,"matTooltip"]],template:function(i,o){1&i&&(e.TgZ(0,"div",0)(1,"button",1),e.NdJ("click",function(){return o.changeDiagramLayout()}),e._UZ(2,"fa-icon",2),e.ALo(3,"translate"),e.ALo(4,"translate"),e.qZA()(),e.TgZ(5,"div",3)(6,"button",1),e.NdJ("click",function(){return o.zoomIn()}),e.TgZ(7,"mat-icon",4),e.ALo(8,"translate"),e._uU(9,"add"),e.qZA()(),e.TgZ(10,"button",1),e.NdJ("click",function(){return o.zoomOut()}),e.TgZ(11,"mat-icon",4),e.ALo(12,"translate"),e._uU(13,"remove"),e.qZA()()()),2&i&&(e.xp6(2),e.Q6J("icon",e.VKq(12,ho,o.isGridLayout?"hashtag-lock":"hashtag"))("matTooltip",o.isGridLayout?e.lcZ(3,4,"toolbar.offGridView"):e.lcZ(4,6,"toolbar.onGridView")),e.xp6(5),e.Q6J("matTooltip",e.lcZ(8,8,"diagram.zoomIn")),e.xp6(4),e.Q6J("matTooltip",e.lcZ(12,10,"diagram.zoomOut")))},dependencies:[Y.gM,j.Hw,R.BN,S.X$],styles:[".zoom-controls[_ngcontent-%COMP%]{position:fixed;bottom:20px;right:7px;display:flex;flex-direction:column;z-index:1000}button[_ngcontent-%COMP%]{width:40px;height:40px;background-color:#e7e7e7;border:1px solid #ccc;cursor:pointer;margin:5px}.zoom-controls[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px;line-height:40px;cursor:pointer}.grid-controls[_ngcontent-%COMP%]{position:fixed;top:50px;right:7px;z-index:1001;display:flex;flex-direction:column}"]})}return n})();var uo=l(5940);const mo=["nameInput"];function fo(n,s){if(1&n&&(e.TgZ(0,"div",6),e._UZ(1,"mat-progress-spinner",7),e.qZA()),2&n){const t=e.oxw();e.xp6(1),e.Q6J("color",t.spinnerColor)("diameter",35)}}function _o(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"span",18),e.NdJ("dblclick",function(){e.CHM(t);const o=e.oxw().$implicit,r=e.oxw(2);return e.KtG(r.startEditing(o))}),e._uU(1),e.qZA()}if(2&n){const t=e.oxw().$implicit;e.ekj("editable",-1!==t.id),e.xp6(1),e.hij(" ",t.name," ")}}function Co(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"form",19),e.NdJ("submit",function(){e.CHM(t);const o=e.oxw().$implicit,r=e.oxw(2);return e.KtG(r.saveEdit(o))}),e.TgZ(1,"input",20,21),e.NdJ("blur",function(){e.CHM(t);const o=e.oxw().$implicit,r=e.oxw(2);return e.KtG(r.saveEdit(o))})("keydown.escape",function(){e.CHM(t);const o=e.oxw(3);return e.KtG(o.cancelEdit())}),e.qZA()()}if(2&n){const t=e.oxw(3);e.xp6(1),e.Q6J("formControl",t.nameEditControl)("maxLength",50)}}function vo(n,s){if(1&n&&(e.TgZ(0,"span",22),e._uU(1),e.qZA()),2&n){const t=e.oxw().$implicit;e.xp6(1),e.hij(" ",t.userName," ")}}const xo=function(n){return{"active-item":n}};function bo(n,s){if(1&n){const t=e.EpF();e.ynx(0),e.TgZ(1,"li",11)(2,"div",12),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(2);return e.KtG(a.previewVersion(r))}),e.TgZ(3,"div",13),e.NdJ("click",function(o){const a=e.CHM(t).$implicit;return e.KtG(-1!==a.id?o.stopPropagation():null)}),e.YNc(4,_o,2,3,"span",14),e.YNc(5,Co,3,2,"form",15),e.qZA(),e.TgZ(6,"span",16),e._uU(7),e.ALo(8,"date"),e.qZA(),e.YNc(9,vo,2,1,"span",17),e.qZA()(),e.BQk()}if(2&n){const t=s.$implicit,i=e.oxw(2);e.xp6(1),e.Q6J("ngClass",e.VKq(8,xo,i.selectedId===t.id)),e.xp6(3),e.Q6J("ngIf",!i.isEditing(t.id)),e.xp6(1),e.Q6J("ngIf",i.isEditing(t.id)),e.xp6(2),e.hij(" ",e.xi3(8,5,t.date,"MMMM d, h:mm a")," "),e.xp6(2),e.Q6J("ngIf",t.userName&&-1!==t.id)}}function yo(n,s){if(1&n&&(e.TgZ(0,"div",8)(1,"ul",9),e.YNc(2,bo,10,10,"ng-container",10),e.qZA()()),2&n){const t=e.oxw();e.xp6(2),e.Q6J("ngForOf",t.versionsHistories)}}let Po=(()=>{class n{constructor(t,i,o,r,a,c,d,g,C,f){this.route=t,this.versionHistoryService=i,this.navbarService=o,this.cardinalityService=r,this.diagramService=a,this.diagramUtils=c,this.projectService=d,this.accessService=g,this.datePipe=C,this.appService=f,this.versionsHistories=[],this.showVersionHistory=!1,this.isLoading=!1,this.projectId=-1,this.currentDiagramId=-1,this.selectedId=-1,this.currentDate=new Date,this.spinnerColors=["primary","accent","warn"],this.spinnerColor=this.spinnerColors[0],this.editValue="",this.editingVersionId=null,this.nameEditControl=new m.NI(""),(0,e.cEC)(()=>{this.showVersionHistory=this.navbarService.showVersionHistory(),this.navbarService.showVersionHistory()||this.clearPreview()},{allowSignalWrites:!0}),this.startColorChange()}startColorChange(){let t=0;setInterval(()=>{this.spinnerColor=this.spinnerColors[t],t=(t+1)%this.spinnerColors.length},800)}ngOnInit(){this.projectId=parseInt(this.route.snapshot.paramMap.get("id")??""),this.retrieveVersions(),this.diagramUtils.activeDiagramChanges().subscribe(t=>{t&&(this.currentDiagramId=t.id)}),this.versionHistoryService.selectedVersion()&&(this.selectedId=this.versionHistoryService.selectedVersion()?.id)}retrieveVersions(){this.isLoading=!0,this.versionHistoryService.getAllVersions(this.projectId).subscribe(t=>{this.versionsHistories=[{idProject:this.projectId,id:-1,name:"Current Changes (Not Released)",date:this.currentDate,userName:"",data:null},...t],this.isLoading=!1})}restoreVersion(){this.navbarService.setShowVersionHistory(!1)}previewVersion(t){this.selectedId=t.id,-1!==t.id?this.versionHistoryService.getVersionHistoryData(t.id).subscribe(i=>{this.versionHistoryService.setSelectedVersion({...t,data:i}),this.editingVersionId=null;const o={...JSON.parse(i).projectDetails,accessType:x.WP.Viewer};this.cardinalityService.clearLinkToLinks(),this.cardinalityService.clearLinks(),this.accessService.setProjectAccess(x.WP.Viewer),this.appService.setIsInitProject(!1),this.projectService.setCurrentProject(o),this.diagramService.getDiagramDetails(this.currentDiagramId)}):this.closeVersionHistory()}closeVersionHistory(){this.navbarService.setShowVersionHistory(!1)}clearPreview(){this.cardinalityService.clearLinkToLinks(),this.cardinalityService.clearLinks(),this.versionHistoryService.setSelectedVersion(null),this.appService.setIsInitProject(!1),this.projectService.openProject(this.projectId)}releaseNewVersion(){this.isLoading=!0;const t=this.datePipe.transform(this.currentDate,"MMMM d, h:mm a")||"";this.versionHistoryService.releaseNewVersion({idProject:this.projectId,name:t}).subscribe({next:i=>{this.versionsHistories.splice(1,0,i),this.isLoading=!1},error:i=>{console.error("Error releasing new version:",i),this.isLoading=!1}})}isEditing(t){return this.editingVersionId===t}startEditing(t){-1!==t.id&&(this.editingVersionId=t.id,this.nameEditControl.setValue(t.name),setTimeout(()=>{this.nameInput&&(this.nameInput.nativeElement.focus(),this.nameInput.nativeElement.select())}))}cancelEdit(){this.editingVersionId=null,this.nameEditControl.reset()}saveEdit(t){if(!this.editingVersionId)return;const i=this.nameEditControl.value?.trim();if(!i||i===t.name)return void this.cancelEdit();const o=this.versionsHistories.findIndex(r=>r.id===t.id);this.versionsHistories[o].name=i,this.versionHistoryService.updateVersion({id:this.versionsHistories[o].id,name:i}),this.cancelEdit()}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(M.gz),e.Y36(Ne.S),e.Y36(ue.I),e.Y36(ee.Q),e.Y36(ge.p),e.Y36(Z.T),e.Y36(H.Y),e.Y36(ie.v),e.Y36(v.uU),e.Y36(je.z))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-version-history"]],viewQuery:function(i,o){if(1&i&&e.Gf(mo,5),2&i){let r;e.iGM(r=e.CRH())&&(o.nameInput=r.first)}},features:[e._Bn([v.uU])],decls:11,vars:8,consts:[["loadingTemplate",""],[1,"version-panel"],[1,"header"],[1,"title"],["matTooltipPosition","left",1,"release-icon",3,"matTooltip","click"],["class","version-container",4,"ngIf","ngIfElse"],[1,"loader-container"],["mode","indeterminate",3,"color","diameter"],[1,"version-container"],[1,"version-list"],[4,"ngFor","ngForOf"],[1,"version-item",3,"ngClass"],[1,"version-info",3,"click"],[1,"version-name-container",3,"click"],["class","version-name",3,"editable","dblclick",4,"ngIf"],["class","edit-form",3,"submit",4,"ngIf"],[1,"version-date"],["class","version-author",4,"ngIf"],[1,"version-name",3,"dblclick"],[1,"edit-form",3,"submit"],["type","text",1,"edit-input",3,"formControl","maxLength","blur","keydown.escape"],["nameInput",""],[1,"version-author"]],template:function(i,o){if(1&i&&(e.YNc(0,fo,2,2,"ng-template",null,0,e.W1O),e.TgZ(2,"div",1)(3,"div",2)(4,"p",3),e._uU(5),e.ALo(6,"translate"),e.qZA(),e.TgZ(7,"mat-icon",4),e.NdJ("click",function(){return o.releaseNewVersion()}),e.ALo(8,"translate"),e._uU(9," rocket_launch "),e.qZA()(),e.YNc(10,yo,3,1,"div",5),e.qZA()),2&i){const r=e.MAs(1);e.xp6(5),e.Oqu(e.lcZ(6,4,"toolbar.versionHistory")),e.xp6(2),e.Q6J("matTooltip",e.lcZ(8,6,"toolbar.releaseNew")),e.xp6(3),e.Q6J("ngIf",!o.isLoading)("ngIfElse",r)}},dependencies:[v.mk,v.sg,v.O5,Y.gM,j.Hw,uo.Ou,m._Y,m.Fj,m.JJ,m.JL,m.F,m.oH,v.uU,S.X$],styles:[".version-panel[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100%;background-color:#f5f4f4}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;position:sticky;top:0;padding-inline:.5rem;z-index:10;padding:8px 16px;background-color:var(--toolbar-bg-color, #f5f5f5);transition:background-color .3s ease}.header[_ngcontent-%COMP%]   .release-icon[_ngcontent-%COMP%]{cursor:pointer;transition:color .3s ease,transform .2s ease}.header[_ngcontent-%COMP%]   .release-icon[_ngcontent-%COMP%]:hover{color:var(--primary-color, #0069b4);transform:scale(1.1)}.title[_ngcontent-%COMP%]{margin:0;font-size:16px;font-weight:500}.tooltip-custom[_ngcontent-%COMP%]{background-color:#333;color:#fff}.tooltip-custom[_ngcontent-%COMP%]   .mat-tooltip[_ngcontent-%COMP%]{font-size:14px}.version-container[_ngcontent-%COMP%]{overflow-y:auto}.version-list[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.version-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:10px;border-bottom:1px solid #e0e0e0;cursor:pointer}.version-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;column-gap:5px;width:100%}.version-name-container[_ngcontent-%COMP%]{position:relative;flex:1;min-width:0}.version-name-container[_ngcontent-%COMP%]   .version-name[_ngcontent-%COMP%]{display:inline-block;padding:3px 100px 3px 0;border-radius:4px;font-size:15px}.version-name-container[_ngcontent-%COMP%]   .version-name.editable[_ngcontent-%COMP%]{cursor:text}.version-name-container[_ngcontent-%COMP%]   .version-name.editable[_ngcontent-%COMP%]:hover{background-color:#0000000a}.version-name-container[_ngcontent-%COMP%]   .edit-form[_ngcontent-%COMP%]{margin:0;padding:0}.version-name-container[_ngcontent-%COMP%]   .edit-input[_ngcontent-%COMP%]{width:100%;padding:3px 1px;border:1px solid #ccc;border-radius:4px;font-size:inherit;font-family:inherit;background:white}.version-name-container[_ngcontent-%COMP%]   .edit-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#0069b4;box-shadow:0 0 0 2px #0069b433}.version-date[_ngcontent-%COMP%]{font-size:12px;margin-bottom:5px;color:#757575;font-style:italic}.version-author[_ngcontent-%COMP%]{font-size:12px;color:#757575}.loader-container[_ngcontent-%COMP%]{display:flex;justify-content:center}.active-item[_ngcontent-%COMP%]{background-color:#dae4f0}"]})}return n})();function wo(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",16)(1,"app-properties",17),e.NdJ("onChangePropertyData",function(o){e.CHM(t);const r=e.oxw();return e.KtG(r.onUpdateProperties(o,r.libraryTreeComponent.selectedTreeNode()))})("isColorPickerSelected",function(o){e.CHM(t);const r=e.oxw();return e.KtG(r.onColorPickerSelection(o))}),e.qZA()()}if(2&n){const t=e.oxw();e.xp6(1),e.Q6J("nodeData",t.propertyData)("editAccess",t.hasEditAccessOnly)("dataTypes",t.attributeTypes)("idDiagram",t.selectedDiagramId)}}function ko(n,s){1&n&&(e.TgZ(0,"mat-drawer-container",18)(1,"mat-drawer",19),e._UZ(2,"app-version-history"),e.qZA()())}const To=function(n){return{"content-margin":n}},Ao=[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"",component:(()=>{class n{static#e=this.\u0275fac=function(i){return new(i||n)};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-home"]],decls:3,vars:0,consts:[[1,"d-flex"],[1,"content-section"]],template:function(i,o){1&i&&(e.TgZ(0,"div",0)(1,"div",1),e._UZ(2,"router-outlet"),e.qZA()())},dependencies:[M.lC],styles:[".d-flex[_ngcontent-%COMP%]{display:flex}.content-section[_ngcontent-%COMP%]{width:100%}"]})}return n})(),children:[{canActivate:[(n,s)=>{const t=(0,e.f3M)(ve),i=(0,e.f3M)(M.F0),o=(0,e.f3M)(st.T_);history.state?.navigationId||localStorage.setItem("copyUrl","true");const a=n.params.id;return!a||t.checkPermission(Number(a)).pipe((0,re.U)(c=>!(!c||c.accessType!==x.WP.Admin&&c.accessType!==x.WP.Editor&&c.accessType!==x.WP.Viewer)||(i.navigate(["/dashboard"]),o.addError({errorKey:403,type:"error",header:"Access Denied",content:"You do not have permission to access this project.",isCustomError:!0}),!1)),(0,E.K)(c=>(console.error("Error checking project permission:",c),404===c.status?(i.navigate(["/dashboard"]),o.addError({errorKey:404,type:"error",header:"Project Not Found",content:"The requested project was not found or you do not have access to it.",isCustomError:!0})):(i.navigate(["/dashboard"]),o.addError({errorKey:500,type:"error",header:"Access Check Failed",content:"Unable to verify project access. Please try again.",isCustomError:!0})),(0,rt.of)(!1))))}],path:"editor/:id/diagram/:idDiagram",component:(()=>{class n{constructor(t,i,o,r,a,c,d,g,C,f,h,_,b,P,y){this.diagramService=t,this.projectService=i,this.propertyService=o,this.route=r,this.router=a,this.linkService=c,this.accessService=d,this.goJsService=g,this.diagramUtils=C,this.loaderService=f,this.treeNodeService=h,this.navbarService=_,this.dialog=b,this.appService=P,this.versionHistoryService=y,this.$=A.bx.make,this.isComponentPanelExpanded=!1,this.isLibraryPanelExpanded=!0,this.diagrams=[],this.selectedDiagramId=-1,this.hasEditAccessOnly=!1,this.attributeTypes=[],this._downloadSub=null,this._downloadAllSub=null,this._currentProjectSub=null,this._colorSelectionSub=null,this._diagramSub=null,this._currentProjDiagramsSub=null,this._deleteDiagramSub=null,this._propertyDataSub=null,this._attributeTypeSub=null,this.showVersionHistory=!1,this.isLoading$=this.loaderService.isLoading$,this._diagramLayoutSub=null,this.selectedNodeTag=u.M.Project,(0,e.cEC)(()=>{this.showVersionHistory=this.navbarService.showVersionHistory()})}ngOnInit(){this.appService.setIsInitProject(!0),A.S0.licenseKey=Ce.N.licenseKey,this.projectId=parseInt(this.route.snapshot.paramMap.get("id")??"");const t=this.route.snapshot.paramMap.get("idDiagram"),i=t?parseInt(t):void 0;this.selectedNodeTag=`atTag${p.J.Diagram}_${i}`,(localStorage.getItem("reloaded")||localStorage.getItem("copyUrl"))&&(this.projectService.openProject(this.projectId,!0,i),this.linkService.initLinkTypes(),localStorage.removeItem("reloaded"),localStorage.removeItem("copyUrl")),this.configureDiagramProperties(),this.initProject(),this.accessService.accessTypeChanges().subscribe(o=>{this.hasEditAccessOnly=o!=x.WP.Viewer}),this.subscribeToDiagramService(),this._attributeTypeSub=this.diagramUtils.getAttributeTypes().subscribe(o=>{this.attributeTypes=o})}unloadHandler(t){t.preventDefault(),this.projectService.handleProjectUnlock(this.projectId),localStorage.setItem("reloaded","true")}configureDiagramProperties(){this.diagram=this.$(A.S0,"diagramDiv",{initialAutoScale:A._O.Uniform,allowCopy:!1,"animationManager.isEnabled":!1,"linkingTool.isEnabled":!0,"draggingTool.isGridSnapEnabled":!0,"linkingTool.isUnconnectedLinkValid":!1,"linkingTool.portGravity":20,"relinkingTool.isUnconnectedLinkValid":!1,"relinkingTool.portGravity":10,"linkReshapingTool.handleArchetype":this.$(A.bn,"Diamond",{desiredSize:new A.$u(7,7),fill:"lightblue",stroke:"#0069B4"}),mouseDrop:t=>this.goJsService.handleDropCompletion(t,null),"commandHandler.archetypeGroupData":{isGroup:!0,text:"Group",horiz:!1},"undoManager.isEnabled":!0,"relinkingTool.isEnabled":!0}),this.diagram.grid=new A.s_("Grid",{gridCellSize:new A.$u(10,10)}).add(new A.bn("LineH",{stroke:"lightgray",strokeWidth:.5,interval:1}),new A.bn("LineH",{stroke:"gray",strokeWidth:.5,interval:5}),new A.bn("LineH",{stroke:"gray",strokeWidth:1,interval:10}),new A.bn("LineV",{stroke:"lightgray",strokeWidth:.5,interval:1}),new A.bn("LineV",{stroke:"gray",strokeWidth:.5,interval:5}),new A.bn("LineV",{stroke:"gray",strokeWidth:1,interval:10}))}initProject(){this._diagramSub=this.diagramUtils.activeDiagramChanges().subscribe(t=>{t&&t.id&&(this.currentDiagram=t,this.selectedDiagramId=this.currentDiagram.id,this.diagramService.getDiagramDetails(t.id))}),this._currentProjDiagramsSub=this.diagramUtils.currentProjectDiagramsChanges().subscribe(t=>{t.length>0&&(this.diagrams=t)}),this._currentProjectSub=this.projectService.currentProjectChanges().subscribe(t=>{t&&(this.linkService.setProjectLinks(t),this.goJsService.initDiagram(this.diagram),this.diagramService.getPaletteDiagramDetails())})}onDiagramSelectionChange(t){this.selectedDiagramId=t;let i=this.diagrams.find(o=>o.id===t);if(!i&&this.project&&this.project.folders){const o=this.findDiagramInFolders(this.project.folders,t);o&&(i=o)}i&&this.diagramUtils.setActiveDiagram(i),this.propertyService.setPropertyData(null),this.router.navigate([`/editor/${this.projectId}/diagram/${t}`],{replaceUrl:!0}),this.selectedNodeTag=`atTag${p.J.Diagram}_${t}`}subscribeToDiagramService(){this._deleteDiagramSub=this.diagramService.deleteDiagramEvent.subscribe(()=>{this.onDeleteDiagram()}),this._downloadSub=this.diagramService.downloadDiagramEvent.subscribe(()=>{this.diagramService.initiateDiagramDownload(!0,!1)}),this._downloadAllSub=this.diagramService.downloadAllDiagramEvent.subscribe(t=>{this.diagramService.initiateDiagramDownload(!1,t),this.onDiagramSelectionChange(this.currentDiagram.id)}),this._propertyDataSub=this.propertyService.propertyDataChanges().subscribe(t=>{t&&(this.propertyData=t)}),this.diagramUtils.currentProjectDiagramsChanges().subscribe(t=>{this.diagram&&(this.diagram.allowDrop=t.length>0)})}onDeleteDiagram(){this.dialog.open(Q.X,{width:"320px",data:{title:"dialog.title",reject:"dialog.no",confirm:"dialog.yes"}}).afterClosed().subscribe(i=>{i&&this.selectedDiagramId&&(this.diagramService.deleteDiagram(this.selectedDiagramId),this.treeNodeService.deleteDiagram(`atTag${p.J.Diagram}_${this.selectedDiagramId}`),this.diagrams=this.diagrams.filter(o=>o.id!==this.selectedDiagramId),this.diagramUtils.setCurrentProjectDiagrams(this.diagrams),this.diagrams.length>0?(this.currentDiagram=this.diagrams[0],this.selectedDiagramId=this.currentDiagram.id,this.diagramUtils.setActiveDiagram(this.currentDiagram),this.router.navigate([`/editor/${this.projectId}/diagram/${this.currentDiagram.id}`],{replaceUrl:!0})):(this.diagramUtils.setActiveDiagram(null),this.router.navigate([`/editor/${this.projectId}/diagram/0`],{replaceUrl:!0})))})}ngOnDestroy(){this.linkService.clearLinks(),this.linkService.clearLinkToLinks(),this.versionHistoryService.setSelectedVersion(null),this.projectService.handleProjectUnlock(this.projectId),this._downloadSub&&this._downloadSub.unsubscribe(),this._downloadAllSub&&this._downloadAllSub.unsubscribe(),this._colorSelectionSub&&this._colorSelectionSub.unsubscribe(),this._diagramSub&&this._diagramSub.unsubscribe(),this._currentProjDiagramsSub&&this._currentProjDiagramsSub.unsubscribe(),this._deleteDiagramSub&&this._deleteDiagramSub.unsubscribe(),this._propertyDataSub&&this._propertyDataSub.unsubscribe(),this._currentProjectSub&&this._currentProjectSub.unsubscribe(),this._attributeTypeSub&&this._attributeTypeSub.unsubscribe(),this._diagramLayoutSub&&this._diagramLayoutSub.unsubscribe(),this.propertyService.setPropertyData(null)}onColorPickerSelection(t){t&&(this.isComponentPanelExpanded=!1,this.isLibraryPanelExpanded=!1)}onUpdateProperties(t,i){this.diagramService.getUpdatedProperties(t,i)}onCreateFolder(t){t.stopPropagation(),this.goJsService.createNewFolder("New Folder",this.projectId),this.libraryTreeComponent.dataSource&&this.libraryTreeComponent.dataSource.data.length>0&&this.libraryTreeComponent.treeControl.expand(this.libraryTreeComponent.dataSource.data[0])}openVersionHistory(){this.navbarService.setShowVersionHistory(!0),this.accessService.setProjectAccess(x.WP.Viewer)}closeVersionHistory(){this.navbarService.setShowVersionHistory(!1)}findDiagramInFolders(t,i){for(const o of t){if(o.diagrams){const r=o.diagrams.find(a=>a.id===i);if(r)return r}if(o.childFolders&&o.childFolders.length>0){const r=this.findDiagramInFolders(o.childFolders,i);if(r)return r}}return null}static#e=this.\u0275fac=function(i){return new(i||n)(e.Y36(ge.p),e.Y36(H.Y),e.Y36(te.b),e.Y36(M.gz),e.Y36(M.F0),e.Y36(ee.Q),e.Y36(ie.v),e.Y36(Li.$),e.Y36(Z.T),e.Y36(Ri.D),e.Y36(he.G),e.Y36(ue.I),e.Y36(k.uw),e.Y36(je.z),e.Y36(Ne.S))};static#t=this.\u0275cmp=e.Xpm({type:n,selectors:[["app-dashboard"]],viewQuery:function(i,o){if(1&i&&e.Gf(Ie,5),2&i){let r;e.iGM(r=e.CRH())&&(o.libraryTreeComponent=r.first)}},hostBindings:function(i,o){1&i&&e.NdJ("beforeunload",function(a){return o.unloadHandler(a)},!1,e.Jf7)},decls:24,vars:21,consts:[[1,"project-container"],["mode","side","opened","",1,"side-container",3,"disableClose"],["multi","true"],["expanded","true","disabled","true",1,"expansion-panel","library-panel",3,"hideToggle"],[1,"disable_ripple"],[1,"panel-title"],["mat-icon-button","","matRippleDisabled","",3,"disabled","click"],[1,"tree-container"],[3,"expandNodeTag"],["expanded","true","disabled","true",1,"expansion-panel",3,"hideToggle"],["id","classDiagram",1,"disable_ripple","component-panel"],["class","property",4,"ngIf"],[3,"ngClass"],["id","diagramDiv",1,"main-container"],[3,"diagram"],["class","version-history-container",4,"ngIf"],[1,"property"],[2,"margin-top","20px","padding-top","20px",3,"nodeData","editAccess","dataTypes","idDiagram","onChangePropertyData","isColorPickerSelected"],[1,"version-history-container"],["mode","over","opened","",1,"version-history-drawer"]],template:function(i,o){1&i&&(e.TgZ(0,"mat-drawer-container",0)(1,"mat-drawer",1)(2,"mat-accordion",2)(3,"mat-expansion-panel",3)(4,"mat-expansion-panel-header",4)(5,"mat-panel-title",5),e._uU(6),e.ALo(7,"translate"),e.qZA(),e.TgZ(8,"button",6),e.NdJ("click",function(a){return o.onCreateFolder(a)}),e.TgZ(9,"mat-icon"),e._uU(10,"create_new_folder"),e.qZA()()(),e.TgZ(11,"div",7),e._UZ(12,"app-library-tree",8),e.qZA()(),e.TgZ(13,"mat-expansion-panel",9)(14,"mat-expansion-panel-header",4)(15,"mat-panel-title",5),e._uU(16),e.ALo(17,"translate"),e.qZA()(),e._UZ(18,"div",10),e.qZA()(),e.YNc(19,wo,2,4,"div",11),e.qZA(),e.TgZ(20,"mat-drawer-content",12),e._UZ(21,"div",13)(22,"app-action-controls",14),e.qZA()(),e.YNc(23,ko,3,0,"mat-drawer-container",15)),2&i&&(e.xp6(1),e.Q6J("disableClose",!0),e.xp6(2),e.Q6J("hideToggle",!0),e.xp6(3),e.hij(" ",e.lcZ(7,15,"diagram.library")," "),e.xp6(2),e.Q6J("disabled",!o.hasEditAccessOnly),e.xp6(4),e.Q6J("expandNodeTag",o.selectedNodeTag),e.xp6(1),e.Udp("visibility",o.showVersionHistory?"hidden":"visible")("height",o.showVersionHistory?"0":"auto"),e.Q6J("hideToggle",!0),e.xp6(3),e.hij(" ",e.lcZ(17,17,"diagram.components")," "),e.xp6(3),e.Q6J("ngIf",o.hasEditAccessOnly),e.xp6(1),e.Q6J("ngClass",e.VKq(19,To,o.showVersionHistory)),e.xp6(2),e.Q6J("diagram",o.diagram),e.xp6(1),e.Q6J("ngIf",o.showVersionHistory))},dependencies:[v.mk,v.O5,J.RK,me.jA,me.kh,me.LW,j.Hw,oe.pp,oe.ib,oe.yz,oe.yK,po,go,Ie,Po,S.X$],styles:['.header-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:auto auto;justify-content:space-between}@media (max-width: 462px){.header-grid[_ngcontent-%COMP%]{grid-template-columns:auto auto;justify-content:space-between;font-size:small}}@media (max-width: 360px){.header-grid[_ngcontent-%COMP%]{font-size:8px;gap:17px;margin:6px}}.header-grid[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.header-grid[_ngcontent-%COMP%]   .btn-dash[_ngcontent-%COMP%]{margin-top:2.5rem;margin-left:.5rem;padding:.8rem;font-size:medium;border-radius:4px;color:#000;cursor:pointer;font-family:Roboto,Helvetica Neue,sans-serif}@media (max-width: 462px){.header-grid[_ngcontent-%COMP%]   .btn-dash[_ngcontent-%COMP%]{margin-top:1.2rem}}.project-container[_ngcontent-%COMP%]{width:100%;height:calc(100vh - var(--toolbar-height));overflow:hidden;border:.5px solid grey;position:relative;top:calc(var(--toolbar-height) - 1px)}.side-container[_ngcontent-%COMP%]{width:15vw;max-width:15vw;background-color:#f5f4f4;overflow:hidden;margin:0}.main-container[_ngcontent-%COMP%]{height:100%;background-color:#fff;overflow:hidden}.disable_ripple[_ngcontent-%COMP%]{background-color:#f5f4f4;padding:0!important;height:2.3rem}.mat-expansion-panel-body[_ngcontent-%COMP%]{padding:0!important}.expansion-panel[_ngcontent-%COMP%]{padding:.4rem;background:transparent}.expansion-panel[_ngcontent-%COMP%]     .mat-expansion-indicator{margin:0 .7rem}#LibraryDiagram[_ngcontent-%COMP%]{height:27vh}.component-panel[_ngcontent-%COMP%]{height:14vh;z-index:0;position:relative}.property[_ngcontent-%COMP%]{position:sticky;top:20px;margin-bottom:1rem;flex:0 0 auto}.panel-title[_ngcontent-%COMP%]{padding:.2rem}.palette_panel[_ngcontent-%COMP%]{width:100%;height:30vh;padding:0}.mat-expansion-indicator[_ngcontent-%COMP%]{position:absolute;left:15px}.menu[_ngcontent-%COMP%]{display:none;position:absolute;width:90px;opacity:0;margin:0;padding:8px 0;z-index:999;box-shadow:0 5px 5px -3px #0003,0 8px 10px 1px #00000024,0 3px 14px 2px #0000001f;list-style:none;background-color:#fff;border-radius:4px}.menu-item[_ngcontent-%COMP%]{display:block;position:relative;min-width:40px;margin:0;padding:6px 16px;font:700 12px sans-serif;color:#000000de;cursor:pointer}.add-menu[_ngcontent-%COMP%]{top:5px}.menu-item[_ngcontent-%COMP%]:before{position:absolute;top:0;left:0;opacity:0;pointer-events:none;content:"";width:100%;height:100%;background-color:#000}.menu-item[_ngcontent-%COMP%]:hover:before{opacity:.04}.menu[_ngcontent-%COMP%]   .menu[_ngcontent-%COMP%]{top:-8px;left:100%;width:-moz-fit-content;width:fit-content}.menu-item[_ngcontent-%COMP%]:hover{background-color:#0069b4;color:#fff}.literal-item[_ngcontent-%COMP%]{display:none}.show-menu[_ngcontent-%COMP%], .menu-item[_ngcontent-%COMP%]:hover > .menu[_ngcontent-%COMP%]{display:block;opacity:1}.submenu-indicator[_ngcontent-%COMP%]{display:none;float:inline-end}#add[_ngcontent-%COMP%]:hover > .submenu-indicator[_ngcontent-%COMP%]{display:inline-block}.loader-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;display:flex;justify-content:center;align-items:center;background-color:#ffffffb3;z-index:1000}.tree-container[_ngcontent-%COMP%]{max-height:320px;overflow-y:auto}mat-expansion-panel-header.mat-expansion-panel-header[aria-disabled=true][_ngcontent-%COMP%]{color:initial}.overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#00000080;z-index:1000}.version-history-container[_ngcontent-%COMP%]{position:fixed;top:var(--toolbar-height);right:0;bottom:0;width:280px;z-index:999;background:#cfcfcf}.version-history-drawer[_ngcontent-%COMP%]{width:100%}.version-history-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:8px;border-bottom:1px solid rgba(0,0,0,.12)}.version-history-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{margin-right:8px}.version-history-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:16px;font-weight:500}.back-button[_ngcontent-%COMP%]{position:fixed;top:1.5px;left:5px;z-index:1011;background-color:#f5f5f5;border-radius:50%;box-shadow:0 2px 4px #0003}.content-margin[_ngcontent-%COMP%]{margin-right:280px}']})}return n})()},{path:"dashboard",component:di}]}];let So=(()=>{class n{static#e=this.\u0275fac=function(i){return new(i||n)};static#t=this.\u0275mod=e.oAB({type:n});static#i=this.\u0275inj=e.cJS({imports:[M.Bz.forChild(Ao),M.Bz]})}return n})(),Mo=(()=>{class n{constructor(t){t.addIconPacks(Ve.yvW),t.addIconPacks(Ze.mRB)}static#e=this.\u0275fac=function(i){return new(i||n)(e.LFG(R.by))};static#t=this.\u0275mod=e.oAB({type:n});static#i=this.\u0275inj=e.cJS({imports:[ot.q,S.aw.forChild(),m.u5,So,R.uH,m.UX,it,nt.m]})}return n})()}}]);