{"ast": null, "code": "import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';\nexport class ErrorMessages {\n  constructor() {\n    this.defaultMessages = [{\n      errorCode: -1,\n      type: 'error',\n      header: _('errors.unhandled.header'),\n      content: _('errors.unhandled.content'),\n      details: ''\n    }, {\n      errorCode: 0,\n      type: 'error',\n      header: _('errors.0.header'),\n      content: _('errors.0.content'),\n      details: ''\n    }, {\n      errorCode: 400,\n      type: 'error',\n      header: _('errors.400.default.header'),\n      content: _('errors.400.default.content'),\n      details: ''\n    }, {\n      errorCode: 401,\n      type: 'error',\n      header: _('errors.401.default.header'),\n      content: _('errors.401.default.content'),\n      details: ''\n    }, {\n      errorCode: 403,\n      type: 'error',\n      header: _('errors.403.default.header'),\n      content: _('errors.403.default.content'),\n      details: ''\n    }, {\n      errorCode: 404,\n      type: 'error',\n      header: _('errors.404.default.header'),\n      content: _('errors.404.default.content'),\n      details: ''\n    }, {\n      errorCode: 500,\n      type: 'error',\n      header: _('errors.500.default.header'),\n      content: _('errors.500.default.content'),\n      details: ''\n    }];\n    this.keyMessages = [];\n  }\n  getMessageByKey(msgKey) {\n    return this.keyMessages.find(msg => msg.key === msgKey);\n  }\n  getDefaultMessageByCode(errorCode, errorDetails) {\n    const defaultMessage = this.defaultMessages.find(msg => msg.errorCode === errorCode);\n    if (defaultMessage) {\n      defaultMessage.details = errorDetails;\n    }\n    return defaultMessage;\n  }\n  getUnknownError(error) {\n    // Handle both legacy and enhanced error formats\n    const errorKey = error.errorKey || error.error?.errorKey;\n    const statusCode = error.statusCode || error.status;\n    const message = error.message || error.error?.message;\n    const unknownError = {\n      key: errorKey,\n      type: 'warn',\n      header: `Error ${statusCode}`,\n      content: message,\n      details: `Add the error key \"${errorKey}\" to handle multilingualism.`\n    };\n    console.warn('[Missing error] - Add this error key to handle multilingualism into the error-messages.ts', unknownError);\n    return unknownError;\n  }\n}", "map": {"version": 3, "names": ["marker", "_", "ErrorMessages", "constructor", "defaultMessages", "errorCode", "type", "header", "content", "details", "keyMessages", "getMessageByKey", "msgKey", "find", "msg", "key", "getDefaultMessageByCode", "errorDetails", "defaultMessage", "getUnknownError", "error", "<PERSON><PERSON><PERSON>", "statusCode", "status", "message", "unknownError", "console", "warn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\components\\dialog-error-message\\error-messages.ts"], "sourcesContent": ["import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';\r\n\r\nexport class ErrorMessages {\r\n  private defaultMessages: Array<DefaultMessage> = [\r\n    {\r\n      errorCode: -1,\r\n      type: 'error',\r\n      header: _('errors.unhandled.header'),\r\n      content: _('errors.unhandled.content'),\r\n      details: '',\r\n    },\r\n    {\r\n      errorCode: 0,\r\n      type: 'error',\r\n      header: _('errors.0.header'),\r\n      content: _('errors.0.content'),\r\n      details: '',\r\n    },\r\n    {\r\n      errorCode: 400,\r\n      type: 'error',\r\n      header: _('errors.400.default.header'),\r\n      content: _('errors.400.default.content'),\r\n      details: '',\r\n    },\r\n    {\r\n      errorCode: 401,\r\n      type: 'error',\r\n      header: _('errors.401.default.header'),\r\n      content: _('errors.401.default.content'),\r\n      details: '',\r\n    },\r\n    {\r\n      errorCode: 403,\r\n      type: 'error',\r\n      header: _('errors.403.default.header'),\r\n      content: _('errors.403.default.content'),\r\n      details: '',\r\n    },\r\n    {\r\n      errorCode: 404,\r\n      type: 'error',\r\n      header: _('errors.404.default.header'),\r\n      content: _('errors.404.default.content'),\r\n      details: '',\r\n    },\r\n    {\r\n      errorCode: 500,\r\n      type: 'error',\r\n      header: _('errors.500.default.header'),\r\n      content: _('errors.500.default.content'),\r\n      details: '',\r\n    },\r\n  ];\r\n\r\n  private keyMessages: Array<KeyMessage> = [];\r\n\r\n  public getMessageByKey(msgKey: string): KeyMessage | undefined {\r\n    return this.keyMessages.find((msg) => msg.key === msgKey);\r\n  }\r\n\r\n  public getDefaultMessageByCode(\r\n    errorCode: number,\r\n    errorDetails: string\r\n  ): DefaultMessage | undefined {\r\n    const defaultMessage = this.defaultMessages.find(\r\n      (msg) => msg.errorCode === errorCode\r\n    );\r\n    if (defaultMessage) {\r\n      defaultMessage.details = errorDetails;\r\n    }\r\n    return defaultMessage;\r\n  }\r\n\r\n  public getUnknownError(error: {\r\n    errorKey?: string;\r\n    error?: { errorKey?: string; message?: string };\r\n    statusCode?: number;\r\n    status?: number;\r\n    message?: string;\r\n  }): KeyMessage {\r\n    // Handle both legacy and enhanced error formats\r\n    const errorKey = error.errorKey || error.error?.errorKey;\r\n    const statusCode = error.statusCode || error.status;\r\n    const message = error.message || error.error?.message;\r\n\r\n    const unknownError = {\r\n      key: errorKey,\r\n      type: 'warn',\r\n      header: `Error ${statusCode}`,\r\n      content: message,\r\n      details: `Add the error key \"${errorKey}\" to handle multilingualism.`,\r\n    };\r\n    console.warn(\r\n      '[Missing error] - Add this error key to handle multilingualism into the error-messages.ts',\r\n      unknownError\r\n    );\r\n    return unknownError;\r\n  }\r\n}\r\n\r\nexport interface KeyMessage {\r\n  key: string;\r\n  type: string;\r\n  header: string;\r\n  content: string;\r\n  details?: string;\r\n  action?: { type: string; name: string };\r\n}\r\n\r\nexport interface DefaultMessage {\r\n  errorCode: number;\r\n  type: string;\r\n  header: string;\r\n  content: string;\r\n  details: string;\r\n}\r\n"], "mappings": "AAAA,SAASA,MAAM,IAAIC,CAAC,QAAQ,yCAAyC;AAErE,OAAM,MAAOC,aAAa;EAA1BC,YAAA;IACU,KAAAC,eAAe,GAA0B,CAC/C;MACEC,SAAS,EAAE,CAAC,CAAC;MACbC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAEN,CAAC,CAAC,yBAAyB,CAAC;MACpCO,OAAO,EAAEP,CAAC,CAAC,0BAA0B,CAAC;MACtCQ,OAAO,EAAE;KACV,EACD;MACEJ,SAAS,EAAE,CAAC;MACZC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAEN,CAAC,CAAC,iBAAiB,CAAC;MAC5BO,OAAO,EAAEP,CAAC,CAAC,kBAAkB,CAAC;MAC9BQ,OAAO,EAAE;KACV,EACD;MACEJ,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAEN,CAAC,CAAC,2BAA2B,CAAC;MACtCO,OAAO,EAAEP,CAAC,CAAC,4BAA4B,CAAC;MACxCQ,OAAO,EAAE;KACV,EACD;MACEJ,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAEN,CAAC,CAAC,2BAA2B,CAAC;MACtCO,OAAO,EAAEP,CAAC,CAAC,4BAA4B,CAAC;MACxCQ,OAAO,EAAE;KACV,EACD;MACEJ,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAEN,CAAC,CAAC,2BAA2B,CAAC;MACtCO,OAAO,EAAEP,CAAC,CAAC,4BAA4B,CAAC;MACxCQ,OAAO,EAAE;KACV,EACD;MACEJ,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAEN,CAAC,CAAC,2BAA2B,CAAC;MACtCO,OAAO,EAAEP,CAAC,CAAC,4BAA4B,CAAC;MACxCQ,OAAO,EAAE;KACV,EACD;MACEJ,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAEN,CAAC,CAAC,2BAA2B,CAAC;MACtCO,OAAO,EAAEP,CAAC,CAAC,4BAA4B,CAAC;MACxCQ,OAAO,EAAE;KACV,CACF;IAEO,KAAAC,WAAW,GAAsB,EAAE;EA4C7C;EA1CSC,eAAeA,CAACC,MAAc;IACnC,OAAO,IAAI,CAACF,WAAW,CAACG,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,GAAG,KAAKH,MAAM,CAAC;EAC3D;EAEOI,uBAAuBA,CAC5BX,SAAiB,EACjBY,YAAoB;IAEpB,MAAMC,cAAc,GAAG,IAAI,CAACd,eAAe,CAACS,IAAI,CAC7CC,GAAG,IAAKA,GAAG,CAACT,SAAS,KAAKA,SAAS,CACrC;IACD,IAAIa,cAAc,EAAE;MAClBA,cAAc,CAACT,OAAO,GAAGQ,YAAY;;IAEvC,OAAOC,cAAc;EACvB;EAEOC,eAAeA,CAACC,KAMtB;IACC;IACA,MAAMC,QAAQ,GAAGD,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACA,KAAK,EAAEC,QAAQ;IACxD,MAAMC,UAAU,GAAGF,KAAK,CAACE,UAAU,IAAIF,KAAK,CAACG,MAAM;IACnD,MAAMC,OAAO,GAAGJ,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACA,KAAK,EAAEI,OAAO;IAErD,MAAMC,YAAY,GAAG;MACnBV,GAAG,EAAEM,QAAQ;MACbf,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,SAASe,UAAU,EAAE;MAC7Bd,OAAO,EAAEgB,OAAO;MAChBf,OAAO,EAAE,sBAAsBY,QAAQ;KACxC;IACDK,OAAO,CAACC,IAAI,CACV,2FAA2F,EAC3FF,YAAY,CACb;IACD,OAAOA,YAAY;EACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}