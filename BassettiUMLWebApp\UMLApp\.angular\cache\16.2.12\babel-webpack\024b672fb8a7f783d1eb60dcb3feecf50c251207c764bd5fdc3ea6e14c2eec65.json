{"ast": null, "code": "import { BehaviorSubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, filter as rxFilter, takeUntil } from 'rxjs/operators';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\n/**\n * Error severity levels\n */\nexport var ErrorSeverity = /*#__PURE__*/function (ErrorSeverity) {\n  ErrorSeverity[\"LOW\"] = \"low\";\n  ErrorSeverity[\"MEDIUM\"] = \"medium\";\n  ErrorSeverity[\"HIGH\"] = \"high\";\n  ErrorSeverity[\"CRITICAL\"] = \"critical\";\n  return ErrorSeverity;\n}(ErrorSeverity || {});\n/**\n * Error categories for better organization\n */\nexport var ErrorCategory = /*#__PURE__*/function (ErrorCategory) {\n  ErrorCategory[\"NETWORK\"] = \"network\";\n  ErrorCategory[\"AUTHENTICATION\"] = \"authentication\";\n  ErrorCategory[\"AUTHORIZATION\"] = \"authorization\";\n  ErrorCategory[\"VALIDATION\"] = \"validation\";\n  ErrorCategory[\"BUSINESS_LOGIC\"] = \"business_logic\";\n  ErrorCategory[\"SYSTEM\"] = \"system\";\n  ErrorCategory[\"USER_INPUT\"] = \"user_input\";\n  ErrorCategory[\"EXTERNAL_SERVICE\"] = \"external_service\";\n  return ErrorCategory;\n}(ErrorCategory || {});\nexport let ErrorService = /*#__PURE__*/(() => {\n  class ErrorService {\n    constructor() {\n      this.destroy$ = new Subject();\n      this.errors$ = new Subject();\n      this.legacyErrors$ = new Subject(); // For backward compatibility\n      this.errorHistory$ = new BehaviorSubject([]);\n      this.statistics$ = new BehaviorSubject(this.initializeStatistics());\n      this.requestBlackList = [];\n      this.errorFilters = [];\n      this.errorHistory = [];\n      this.config = {\n        enableLogging: !environment.production,\n        enableAnalytics: true,\n        maxErrorHistory: 100,\n        debounceTime: 300,\n        defaultRetryAttempts: 3,\n        logLevel: environment.production ? 'error' : 'debug'\n      };\n      this.initializeErrorHandling();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      this.errors$.complete();\n      this.legacyErrors$.complete();\n      this.errorHistory$.complete();\n      this.statistics$.complete();\n    }\n    /**\n     * Initialize error handling pipeline\n     */\n    initializeErrorHandling() {\n      this.errors$.pipe(takeUntil(this.destroy$), debounceTime(this.config.debounceTime || 300), distinctUntilChanged((prev, curr) => prev.message === curr.message && prev.statusCode === curr.statusCode && Math.abs(curr.timestamp.getTime() - prev.timestamp.getTime()) < 5000)).subscribe(error => {\n        this.processError(error);\n      });\n    }\n    /**\n     * Add error with enhanced metadata and processing\n     */\n    addError(error, context) {\n      const enhancedError = this.createEnhancedError(error, context);\n      if (this.shouldSuppressError(enhancedError)) {\n        return enhancedError.id;\n      }\n      this.errors$.next(enhancedError);\n      return enhancedError.id;\n    }\n    /**\n     * Add custom error with specific configuration\n     */\n    addCustomError(message, category = ErrorCategory.SYSTEM, severity = ErrorSeverity.MEDIUM, context, options) {\n      const customError = {\n        isCustomError: true,\n        message,\n        category,\n        severity,\n        error: {\n          errorKey: options?.errorKey,\n          message\n        },\n        ...options\n      };\n      return this.addError(customError, {\n        ...context,\n        component: context?.component || 'custom'\n      });\n    }\n    /**\n     * Get errors observable with optional filtering\n     */\n    getErrors(errorFilter) {\n      return this.errors$.pipe(rxFilter(error => this.matchesFilter(error, errorFilter)), takeUntil(this.destroy$));\n    }\n    /**\n     * Get errors observable (backward compatibility - returns legacy format)\n     * @deprecated Use getErrors() instead for enhanced error handling\n     */\n    getErrorsLegacy() {\n      return this.legacyErrors$.pipe(takeUntil(this.destroy$));\n    }\n    /**\n     * Get error history\n     */\n    getErrorHistory() {\n      return this.errorHistory$.asObservable();\n    }\n    /**\n     * Get error statistics\n     */\n    getStatistics() {\n      return this.statistics$.asObservable();\n    }\n    /**\n     * Register unhandled URL to the blacklist (backward compatibility)\n     */\n    registerUnhandledRequestURL(url) {\n      this.requestBlackList.push(url);\n    }\n    /**\n     * Check if request URL is blacklisted (backward compatibility)\n     */\n    isRequestBlackListed(url) {\n      return this.requestBlackList.some(rq => url.includes(rq));\n    }\n    /**\n     * Add error filter\n     */\n    addErrorFilter(filter) {\n      this.errorFilters.push(filter);\n    }\n    /**\n     * Remove error filter\n     */\n    removeErrorFilter(filter) {\n      const index = this.errorFilters.indexOf(filter);\n      if (index > -1) {\n        this.errorFilters.splice(index, 1);\n      }\n    }\n    /**\n     * Clear all error filters\n     */\n    clearErrorFilters() {\n      this.errorFilters = [];\n    }\n    /**\n     * Update service configuration\n     */\n    updateConfig(config) {\n      this.config = {\n        ...this.config,\n        ...config\n      };\n    }\n    /**\n     * Clear error history\n     */\n    clearErrorHistory() {\n      this.errorHistory = [];\n      this.errorHistory$.next([]);\n    }\n    /**\n     * Mark error as handled\n     */\n    markErrorAsHandled(errorId) {\n      const error = this.errorHistory.find(e => e.id === errorId);\n      if (error) {\n        error.handled = true;\n        this.errorHistory$.next([...this.errorHistory]);\n      }\n    }\n    /**\n     * Retry failed operation\n     */\n    retryError(errorId, retryFunction) {\n      const error = this.errorHistory.find(e => e.id === errorId);\n      if (!error || !error.retryable) {\n        throw new Error('Error is not retryable or not found');\n      }\n      error.retryCount = (error.retryCount || 0) + 1;\n      if (error.retryCount > (error.maxRetries || this.config.defaultRetryAttempts || 3)) {\n        throw new Error('Maximum retry attempts exceeded');\n      }\n      return retryFunction();\n    }\n    /**\n     * Initialize statistics object\n     */\n    initializeStatistics() {\n      return {\n        totalErrors: 0,\n        errorsByCategory: {},\n        errorsBySeverity: {},\n        errorsByStatusCode: {},\n        mostCommonErrors: [],\n        errorTrends: []\n      };\n    }\n    /**\n     * Create enhanced error from raw error\n     */\n    createEnhancedError(error, context) {\n      const timestamp = new Date();\n      const id = this.generateErrorId();\n      // Extract error information using type guards\n      const isHttpError = this.isHttpErrorResponse(error);\n      const isLegacyError = this.isLegacyError(error);\n      const isCustomError = isLegacyError && error.isCustomError === true;\n      let message = '';\n      let statusCode;\n      let errorKey;\n      let category = ErrorCategory.SYSTEM;\n      let severity = ErrorSeverity.MEDIUM;\n      if (isCustomError && isLegacyError) {\n        message = error.message || 'Custom error occurred';\n        errorKey = error.error?.errorKey;\n        category = this.determineCategory(error);\n        severity = this.determineSeverity(error);\n      } else if (isHttpError) {\n        statusCode = error.status;\n        message = error.error?.message || error.message || `HTTP ${statusCode} Error`;\n        errorKey = error.error?.errorKey;\n        category = this.categorizeHttpError(statusCode || 0);\n        severity = this.determineSeverityFromStatus(statusCode || 0);\n      } else if (isLegacyError) {\n        statusCode = error.status || error.statusCode;\n        message = error.message || 'Legacy error occurred';\n        errorKey = error.error?.errorKey;\n        category = statusCode ? this.categorizeHttpError(statusCode) : ErrorCategory.SYSTEM;\n        severity = statusCode ? this.determineSeverityFromStatus(statusCode) : ErrorSeverity.MEDIUM;\n      } else {\n        // Standard Error object\n        message = error.message || 'Unknown error occurred';\n        category = ErrorCategory.SYSTEM;\n        severity = ErrorSeverity.HIGH;\n      }\n      const enhancedError = {\n        id,\n        originalError: error,\n        message,\n        severity,\n        category,\n        context: {\n          timestamp,\n          url: window.location.href,\n          userAgent: navigator.userAgent,\n          ...context\n        },\n        isCustomError,\n        errorKey,\n        statusCode,\n        retryable: this.isRetryable(error, statusCode),\n        retryCount: 0,\n        maxRetries: this.config.defaultRetryAttempts,\n        suppressDialog: isLegacyError && error.suppressDialog || false,\n        handled: false,\n        timestamp\n      };\n      return enhancedError;\n    }\n    /**\n     * Process error through the pipeline\n     */\n    processError(error) {\n      // Add to history\n      this.addToHistory(error);\n      // Update statistics\n      this.updateStatistics(error);\n      // Log error if enabled\n      if (this.config.enableLogging && !this.shouldSuppressLogging(error)) {\n        this.logError(error);\n      }\n      // Emit for dialog display (backward compatibility)\n      if (!error.suppressDialog) {\n        // Convert back to original format for backward compatibility\n        const legacyError = this.convertToLegacyFormat(error);\n        this.legacyErrors$.next(legacyError);\n        console.debug('Legacy error format for dialog:', legacyError);\n      }\n    }\n    /**\n     * Check if error should be suppressed\n     */\n    shouldSuppressError(error) {\n      // Check URL blacklist\n      if (error.context.url && this.isRequestBlackListed(error.context.url)) {\n        return true;\n      }\n      // Check error filters\n      return this.errorFilters.some(filter => this.matchesFilter(error, filter));\n    }\n    /**\n     * Check if error matches filter\n     */\n    matchesFilter(error, filter) {\n      if (!filter) return true;\n      if (filter.statusCodes && error.statusCode && !filter.statusCodes.includes(error.statusCode)) {\n        return false;\n      }\n      if (filter.errorKeys && error.errorKey && !filter.errorKeys.includes(error.errorKey)) {\n        return false;\n      }\n      if (filter.categories && !filter.categories.includes(error.category)) {\n        return false;\n      }\n      if (filter.severities && !filter.severities.includes(error.severity)) {\n        return false;\n      }\n      if (filter.urlPatterns && error.context.url) {\n        const matchesUrl = filter.urlPatterns.some(pattern => error.context.url.includes(pattern));\n        if (!matchesUrl) return false;\n      }\n      return true;\n    }\n    /**\n     * Check if logging should be suppressed for this error\n     */\n    shouldSuppressLogging(error) {\n      return this.errorFilters.some(filter => filter.suppressLogging && this.matchesFilter(error, filter));\n    }\n    /**\n     * Add error to history with size management\n     */\n    addToHistory(error) {\n      this.errorHistory.unshift(error);\n      // Maintain max history size\n      if (this.errorHistory.length > (this.config.maxErrorHistory || 100)) {\n        this.errorHistory = this.errorHistory.slice(0, this.config.maxErrorHistory || 100);\n      }\n      this.errorHistory$.next([...this.errorHistory]);\n    }\n    /**\n     * Update error statistics\n     */\n    updateStatistics(error) {\n      const currentStats = this.statistics$.value;\n      // Update total count\n      currentStats.totalErrors++;\n      // Update category count\n      currentStats.errorsByCategory[error.category] = (currentStats.errorsByCategory[error.category] || 0) + 1;\n      // Update severity count\n      currentStats.errorsBySeverity[error.severity] = (currentStats.errorsBySeverity[error.severity] || 0) + 1;\n      // Update status code count\n      if (error.statusCode) {\n        currentStats.errorsByStatusCode[error.statusCode] = (currentStats.errorsByStatusCode[error.statusCode] || 0) + 1;\n      }\n      // Update most common errors\n      if (error.errorKey) {\n        const existingError = currentStats.mostCommonErrors.find(e => e.errorKey === error.errorKey);\n        if (existingError) {\n          existingError.count++;\n        } else {\n          currentStats.mostCommonErrors.push({\n            errorKey: error.errorKey,\n            count: 1\n          });\n        }\n        // Sort and keep top 10\n        currentStats.mostCommonErrors.sort((a, b) => b.count - a.count);\n        currentStats.mostCommonErrors = currentStats.mostCommonErrors.slice(0, 10);\n      }\n      // Update trends (hourly buckets)\n      const hourBucket = new Date(error.timestamp);\n      hourBucket.setMinutes(0, 0, 0);\n      const existingTrend = currentStats.errorTrends.find(t => t.timestamp.getTime() === hourBucket.getTime());\n      if (existingTrend) {\n        existingTrend.count++;\n      } else {\n        currentStats.errorTrends.push({\n          timestamp: hourBucket,\n          count: 1\n        });\n      }\n      // Keep only last 24 hours\n      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\n      currentStats.errorTrends = currentStats.errorTrends.filter(t => t.timestamp >= oneDayAgo);\n      this.statistics$.next(currentStats);\n    }\n    /**\n     * Log error based on configuration\n     */\n    logError(error) {\n      const logLevel = this.config.logLevel || 'error';\n      const logMessage = `[${error.severity.toUpperCase()}] ${error.category}: ${error.message}`;\n      const logData = {\n        id: error.id,\n        category: error.category,\n        severity: error.severity,\n        statusCode: error.statusCode,\n        errorKey: error.errorKey,\n        context: error.context,\n        originalError: error.originalError\n      };\n      switch (logLevel) {\n        case 'debug':\n          console.debug(logMessage, logData);\n          break;\n        case 'info':\n          if (error.severity === ErrorSeverity.LOW) {\n            console.info(logMessage, logData);\n          } else {\n            console.error(logMessage, logData);\n          }\n          break;\n        case 'warn':\n          if (error.severity === ErrorSeverity.LOW || error.severity === ErrorSeverity.MEDIUM) {\n            console.warn(logMessage, logData);\n          } else {\n            console.error(logMessage, logData);\n          }\n          break;\n        case 'error':\n        default:\n          console.error(logMessage, logData);\n          break;\n      }\n    }\n    /**\n     * Generate unique error ID\n     */\n    generateErrorId() {\n      return `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n    }\n    /**\n     * Determine error category from custom error\n     */\n    determineCategory(error) {\n      const errorKey = error.error?.errorKey?.toLowerCase() || '';\n      if (errorKey.includes('auth') || errorKey.includes('login') || errorKey.includes('token')) {\n        return ErrorCategory.AUTHENTICATION;\n      }\n      if (errorKey.includes('permission') || errorKey.includes('access') || errorKey.includes('forbidden')) {\n        return ErrorCategory.AUTHORIZATION;\n      }\n      if (errorKey.includes('validation') || errorKey.includes('invalid') || errorKey.includes('required')) {\n        return ErrorCategory.VALIDATION;\n      }\n      if (errorKey.includes('business') || errorKey.includes('rule') || errorKey.includes('logic')) {\n        return ErrorCategory.BUSINESS_LOGIC;\n      }\n      if (errorKey.includes('input') || errorKey.includes('user')) {\n        return ErrorCategory.USER_INPUT;\n      }\n      if (errorKey.includes('external') || errorKey.includes('service') || errorKey.includes('api')) {\n        return ErrorCategory.EXTERNAL_SERVICE;\n      }\n      return ErrorCategory.SYSTEM;\n    }\n    /**\n     * Determine error severity from custom error\n     */\n    determineSeverity(error) {\n      const errorKey = error.error?.errorKey?.toLowerCase() || '';\n      if (errorKey.includes('critical') || errorKey.includes('fatal')) {\n        return ErrorSeverity.CRITICAL;\n      }\n      if (errorKey.includes('high') || errorKey.includes('severe')) {\n        return ErrorSeverity.HIGH;\n      }\n      if (errorKey.includes('low') || errorKey.includes('minor')) {\n        return ErrorSeverity.LOW;\n      }\n      return ErrorSeverity.MEDIUM;\n    }\n    /**\n     * Categorize HTTP error by status code\n     */\n    categorizeHttpError(statusCode) {\n      if (statusCode === 401) {\n        return ErrorCategory.AUTHENTICATION;\n      }\n      if (statusCode === 403) {\n        return ErrorCategory.AUTHORIZATION;\n      }\n      if (statusCode >= 400 && statusCode < 500) {\n        return ErrorCategory.VALIDATION;\n      }\n      if (statusCode >= 500) {\n        return ErrorCategory.SYSTEM;\n      }\n      if (statusCode === 0 || statusCode < 0) {\n        return ErrorCategory.NETWORK;\n      }\n      return ErrorCategory.SYSTEM;\n    }\n    /**\n     * Determine severity from HTTP status code\n     */\n    determineSeverityFromStatus(statusCode) {\n      if (statusCode >= 500) {\n        return ErrorSeverity.HIGH;\n      }\n      if (statusCode === 401 || statusCode === 403) {\n        return ErrorSeverity.MEDIUM;\n      }\n      if (statusCode === 404) {\n        return ErrorSeverity.LOW;\n      }\n      if (statusCode >= 400 && statusCode < 500) {\n        return ErrorSeverity.MEDIUM;\n      }\n      if (statusCode === 0 || statusCode < 0) {\n        return ErrorSeverity.HIGH;\n      }\n      return ErrorSeverity.MEDIUM;\n    }\n    /**\n     * Check if error is retryable\n     */\n    isRetryable(_error, statusCode) {\n      // Network errors are usually retryable\n      if (statusCode === 0 || statusCode === undefined) {\n        return true;\n      }\n      // Server errors are retryable\n      if (statusCode >= 500) {\n        return true;\n      }\n      // Timeout errors are retryable\n      if (statusCode === 408 || statusCode === 504) {\n        return true;\n      }\n      // Rate limiting is retryable\n      if (statusCode === 429) {\n        return true;\n      }\n      // Client errors are generally not retryable\n      if (statusCode >= 400 && statusCode < 500) {\n        return false;\n      }\n      return false;\n    }\n    /**\n     * Convert enhanced error back to legacy format for backward compatibility\n     */\n    convertToLegacyFormat(error) {\n      return {\n        ...error.originalError,\n        status: error.statusCode,\n        error: {\n          errorKey: error.errorKey,\n          message: error.message,\n          errorDetails: error.context.additionalData\n        },\n        isCustomError: error.isCustomError\n      };\n    }\n    /**\n     * Type guard to check if error is HttpErrorResponse\n     */\n    isHttpErrorResponse(error) {\n      return error && typeof error.status === 'number' && error.error !== undefined;\n    }\n    /**\n     * Type guard to check if error is LegacyError\n     */\n    isLegacyError(error) {\n      return error && (error.status !== undefined || error.statusCode !== undefined || error.isCustomError !== undefined);\n    }\n    static #_ = this.ɵfac = function ErrorService_Factory(t) {\n      return new (t || ErrorService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ErrorService,\n      factory: ErrorService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ErrorService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}