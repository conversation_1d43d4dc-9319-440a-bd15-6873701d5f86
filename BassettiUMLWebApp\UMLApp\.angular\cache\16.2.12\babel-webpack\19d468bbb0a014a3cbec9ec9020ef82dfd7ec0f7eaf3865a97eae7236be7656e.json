{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/errors/error.service\";\nimport * as i2 from \"../services/user/user.service\";\nexport let HttpErrorInterceptor = /*#__PURE__*/(() => {\n  class HttpErrorInterceptor {\n    constructor(errorService, userService) {\n      this.errorService = errorService;\n      this.userService = userService;\n    }\n    intercept(request, next) {\n      if (!this.errorService.isRequestBlackListed(request.url)) {\n        return next.handle(request).pipe(catchError(errorObj => {\n          if (errorObj.status == 401) {\n            return this.handleUnauthorizedError(request, next);\n          } else if (errorObj.status == 404 && errorObj.error?.type === 'RefreshToken' || errorObj.status === 400 && errorObj.error?.errorKey === 'INVALID_GRANT') {\n            this.userService.logout(true);\n            return throwError(() => errorObj);\n          } else if (errorObj.status == 400 && (errorObj.error?.errorKey == 'ALREADY_DELETED_EVENT' || errorObj.error?.errorKey == 'ALREADY_EXIST')) {\n            return throwError(() => errorObj);\n          } else {\n            this.errorService.addError(errorObj);\n            return throwError(() => errorObj);\n          }\n        }));\n      } else {\n        return next.handle(request);\n      }\n    }\n    /**\n     * Handles an unauthorized error in an HTTP request by refreshing the access token,\n     * updating the tokens, and creating a new request with the updated tokens.\n     *\n     * @private\n     * @param {HttpRequest<any>} request The original HTTP request.\n     * @param {HttpHandler} next The HTTP handler for the next interceptor in the chain.\n     * @return  {Observable<HttpEvent<any>>}\n     * @memberof HttpErrorInterceptor\n     */\n    handleUnauthorizedError(request, next) {\n      return this.userService.refreshToken().pipe(switchMap(() => {\n        const newRequest = request.clone({\n          withCredentials: true\n          // setHeaders: {\n          //   Authorization: environment.bearerToken,\n          // },\n        });\n        return next.handle(newRequest);\n      }), catchError(errObj => {\n        this.errorService.addError(errObj);\n        this.userService.logout(true);\n        return throwError(() => errObj);\n      }));\n    }\n    static #_ = this.ɵfac = function HttpErrorInterceptor_Factory(t) {\n      return new (t || HttpErrorInterceptor)(i0.ɵɵinject(i1.ErrorService), i0.ɵɵinject(i2.UserService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: HttpErrorInterceptor,\n      factory: HttpErrorInterceptor.ɵfac\n    });\n  }\n  return HttpErrorInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}