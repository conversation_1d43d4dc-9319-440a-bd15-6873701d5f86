{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { catchError, map, of } from 'rxjs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { PermissionApiService } from '../services/api/permission-api.service';\nimport { ErrorService } from '../services/errors/error.service';\nexport const accessGuard = (route, _state) => {\n  const permissionApiService = inject(PermissionApiService);\n  const router = inject(Router);\n  const errorService = inject(ErrorService);\n  const navigation = history.state?.navigationId;\n  if (!navigation) {\n    localStorage.setItem('copyUrl', 'true');\n  }\n  // Extract project ID from route parameters\n  const projectId = route.params['id'];\n  if (!projectId) {\n    // If no project ID, allow access (for dashboard, etc.)\n    return true;\n  }\n  // Note: We now allow access if user has any permission level\n  // Specific permission checks are handled within the application components\n  // Check user permission for the project\n  return permissionApiService.checkPermission(Number(projectId)).pipe(map(permission => {\n    // If user has any permission (View, Edit, or Admin), allow access to view the project\n    if (permission && (permission.accessType === AccessType.Admin || permission.accessType === AccessType.Editor || permission.accessType === AccessType.Viewer)) {\n      return true;\n    } else {\n      // User doesn't have any permission, redirect to dashboard\n      router.navigate(['/dashboard']);\n      errorService.addError({\n        errorKey: 403,\n        type: 'error',\n        header: 'Access Denied',\n        content: 'You do not have permission to access this project.',\n        isCustomError: true\n      });\n      return false;\n    }\n  }), catchError(error => {\n    // Handle permission check error\n    console.error('Error checking project permission:', error);\n    if (error.status === 404) {\n      // Project not found or user has no permission\n      router.navigate(['/dashboard']);\n      errorService.addError({\n        errorKey: 404,\n        type: 'error',\n        header: 'Project Not Found',\n        content: 'The requested project was not found or you do not have access to it.',\n        isCustomError: true\n      });\n    } else {\n      // Other errors\n      router.navigate(['/dashboard']);\n      errorService.addError({\n        errorKey: 500,\n        type: 'error',\n        header: 'Access Check Failed',\n        content: 'Unable to verify project access. Please try again.',\n        isCustomError: true\n      });\n    }\n    return of(false);\n  }));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}