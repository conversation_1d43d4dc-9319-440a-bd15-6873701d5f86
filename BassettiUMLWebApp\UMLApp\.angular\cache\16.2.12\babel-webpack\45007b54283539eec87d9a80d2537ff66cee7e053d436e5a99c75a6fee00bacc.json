{"ast": null, "code": "import { BehaviorSubject, Subject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport var ErrorSeverity;\n(function (ErrorSeverity) {\n  ErrorSeverity[\"LOW\"] = \"LOW\";\n  ErrorSeverity[\"MEDIUM\"] = \"MEDIUM\";\n  ErrorSeverity[\"HIGH\"] = \"HIGH\";\n  ErrorSeverity[\"CRITICAL\"] = \"CRITICAL\";\n})(ErrorSeverity || (ErrorSeverity = {}));\nexport class ErrorService {\n  constructor() {\n    this.errors$ = new Subject();\n    this.errorHistory$ = new BehaviorSubject([]);\n    this.requestBlackList = new Set();\n    this.maxHistorySize = 100;\n  }\n  /**\n   * Add error to the error stream\n   * @param error Error object with your custom structure or raw error data\n   */\n  addError(error) {\n    const normalizedError = this.normalizeError(error);\n    // Don't process blacklisted URLs\n    if (normalizedError.url && this.isRequestBlackListed(normalizedError.url)) {\n      return;\n    }\n    this.errors$.next(normalizedError);\n    this.addToHistory(normalizedError);\n  }\n  /**\n   * Get error observable stream\n   */\n  getErrors() {\n    return this.errors$.asObservable();\n  }\n  /**\n   * Get errors filtered by type\n   */\n  getErrorsByType(type) {\n    return this.errors$.pipe(filter(error => error.type === type));\n  }\n  /**\n   * Get errors filtered by severity\n   */\n  getErrorsBySeverity(severity) {\n    return this.errors$.pipe(filter(error => error.severity === severity));\n  }\n  /**\n   * Get custom errors only\n   */\n  getCustomErrors() {\n    return this.errors$.pipe(filter(error => error.isCustomError === true));\n  }\n  /**\n   * Get error history\n   */\n  getErrorHistory() {\n    return this.errorHistory$.asObservable();\n  }\n  /**\n   * Clear error history\n   */\n  clearErrorHistory() {\n    this.errorHistory$.next([]);\n  }\n  /**\n   * Register URL to blacklist for error handling\n   * @param url URL pattern to ignore\n   */\n  registerUnhandledRequestURL(url) {\n    if (url && url.trim()) {\n      this.requestBlackList.add(url.trim());\n    }\n  }\n  /**\n   * Remove URL from blacklist\n   * @param url URL pattern to remove from blacklist\n   */\n  removeFromBlacklist(url) {\n    this.requestBlackList.delete(url);\n  }\n  /**\n   * Check if request URL is blacklisted\n   * @param url URL to check\n   */\n  isRequestBlackListed(url) {\n    if (!url) return false;\n    return Array.from(this.requestBlackList).some(blacklistedUrl => url.includes(blacklistedUrl));\n  }\n  /**\n   * Get all blacklisted URLs\n   */\n  getBlacklistedUrls() {\n    return Array.from(this.requestBlackList);\n  }\n  /**\n   * Clear all blacklisted URLs\n   */\n  clearBlacklist() {\n    this.requestBlackList.clear();\n  }\n  /**\n   * Create a standardized access denied error\n   */\n  createAccessDeniedError(content) {\n    return {\n      id: this.generateErrorId(),\n      errorKey: 403,\n      type: 'error',\n      header: 'Access Denied',\n      content: content || 'You do not have permission to access this resource.',\n      isCustomError: true,\n      timestamp: new Date(),\n      statusCode: 403,\n      severity: ErrorSeverity.HIGH\n    };\n  }\n  /**\n   * Create a validation error\n   */\n  createValidationError(header, content, errorKey) {\n    return {\n      id: this.generateErrorId(),\n      errorKey: errorKey || 'validation',\n      type: 'validation',\n      header,\n      content,\n      isCustomError: true,\n      timestamp: new Date(),\n      severity: ErrorSeverity.MEDIUM\n    };\n  }\n  /**\n   * Create a success message\n   */\n  createSuccessMessage(header, content) {\n    return {\n      id: this.generateErrorId(),\n      errorKey: 'success',\n      type: 'success',\n      header,\n      content,\n      isCustomError: true,\n      timestamp: new Date(),\n      severity: ErrorSeverity.LOW\n    };\n  }\n  /**\n   * Create a warning message\n   */\n  createWarningMessage(header, content, errorKey) {\n    return {\n      id: this.generateErrorId(),\n      errorKey: errorKey || 'warning',\n      type: 'warning',\n      header,\n      content,\n      isCustomError: true,\n      timestamp: new Date(),\n      severity: ErrorSeverity.MEDIUM\n    };\n  }\n  /**\n   * Create an info message\n   */\n  createInfoMessage(header, content) {\n    return {\n      id: this.generateErrorId(),\n      errorKey: 'info',\n      type: 'info',\n      header,\n      content,\n      isCustomError: true,\n      timestamp: new Date(),\n      severity: ErrorSeverity.LOW\n    };\n  }\n  /**\n   * Create HTTP error from response\n   */\n  createHttpError(statusCode, url, details) {\n    const errorInfo = this.getHttpErrorInfo(statusCode);\n    return {\n      id: this.generateErrorId(),\n      errorKey: statusCode,\n      type: 'error',\n      header: errorInfo.header,\n      content: errorInfo.content,\n      isCustomError: false,\n      timestamp: new Date(),\n      url,\n      statusCode,\n      details,\n      severity: this.getHttpErrorSeverity(statusCode)\n    };\n  }\n  /**\n   * Normalize different error formats into AppError\n   */\n  normalizeError(error) {\n    // If it's already in the expected format\n    if (this.isLegacyError(error)) {\n      return {\n        id: this.generateErrorId(),\n        timestamp: new Date(),\n        severity: this.determineSeverity(error),\n        ...error\n      };\n    }\n    // Handle HTTP errors\n    if (error?.status !== undefined) {\n      return this.createHttpError(error.status, error.url, error);\n    }\n    // Handle Angular HttpErrorResponse\n    if (error?.error && error?.status) {\n      return this.createHttpError(error.status, error.url, error.error);\n    }\n    // Handle generic errors\n    return {\n      id: this.generateErrorId(),\n      errorKey: 'unknown',\n      type: 'error',\n      header: 'Unexpected Error',\n      content: error?.message || error?.toString() || 'An unexpected error occurred',\n      isCustomError: false,\n      timestamp: new Date(),\n      details: error,\n      severity: ErrorSeverity.HIGH\n    };\n  }\n  /**\n   * Check if object matches your legacy error structure\n   */\n  isLegacyError(obj) {\n    return obj && typeof obj.type === 'string' && typeof obj.header === 'string' && typeof obj.content === 'string';\n  }\n  /**\n   * Determine severity based on error properties\n   */\n  determineSeverity(error) {\n    if (error.severity) return error.severity;\n    if (error.type === 'error') {\n      if (error.errorKey === 403 || error.errorKey === 401) return ErrorSeverity.HIGH;\n      if (error.errorKey === 404) return ErrorSeverity.MEDIUM;\n      return ErrorSeverity.HIGH;\n    }\n    if (error.type === 'warning') return ErrorSeverity.MEDIUM;\n    if (error.type === 'success' || error.type === 'info') return ErrorSeverity.LOW;\n    return ErrorSeverity.MEDIUM;\n  }\n  /**\n   * Get HTTP error information\n   */\n  getHttpErrorInfo(statusCode) {\n    const errorMap = {\n      400: {\n        header: 'Bad Request',\n        content: 'Invalid request. Please check your input and try again.'\n      },\n      401: {\n        header: 'Unauthorized',\n        content: 'You need to log in to access this resource.'\n      },\n      403: {\n        header: 'Access Denied',\n        content: 'You do not have permission to access this resource.'\n      },\n      404: {\n        header: 'Not Found',\n        content: 'The requested resource could not be found.'\n      },\n      409: {\n        header: 'Conflict',\n        content: 'A conflict occurred. The resource may have been modified by another user.'\n      },\n      422: {\n        header: 'Validation Error',\n        content: 'The provided data is invalid. Please check your input.'\n      },\n      429: {\n        header: 'Too Many Requests',\n        content: 'Too many requests. Please wait a moment and try again.'\n      },\n      500: {\n        header: 'Server Error',\n        content: 'An internal server error occurred. Please try again later.'\n      },\n      502: {\n        header: 'Service Unavailable',\n        content: 'The service is temporarily unavailable. Please try again later.'\n      },\n      503: {\n        header: 'Service Unavailable',\n        content: 'The service is temporarily unavailable. Please try again later.'\n      },\n      504: {\n        header: 'Request Timeout',\n        content: 'The request timed out. Please try again.'\n      }\n    };\n    return errorMap[statusCode] || {\n      header: 'Error',\n      content: `An error occurred (Status: ${statusCode}). Please try again.`\n    };\n  }\n  /**\n   * Get severity for HTTP status codes\n   */\n  getHttpErrorSeverity(statusCode) {\n    if (statusCode >= 500) return ErrorSeverity.CRITICAL;\n    if (statusCode === 403 || statusCode === 401) return ErrorSeverity.HIGH;\n    if (statusCode >= 400) return ErrorSeverity.MEDIUM;\n    return ErrorSeverity.LOW;\n  }\n  /**\n   * Add error to history with size limit\n   */\n  addToHistory(error) {\n    const currentHistory = this.errorHistory$.value;\n    const newHistory = [error, ...currentHistory].slice(0, this.maxHistorySize);\n    this.errorHistory$.next(newHistory);\n  }\n  /**\n   * Generate unique error ID\n   */\n  generateErrorId() {\n    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n  /**\n   * Clean up resources\n   */\n  destroy() {\n    this.errors$.complete();\n    this.errorHistory$.complete();\n    this.requestBlackList.clear();\n  }\n  static #_ = this.ɵfac = function ErrorService_Factory(t) {\n    return new (t || ErrorService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ErrorService,\n    factory: ErrorService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Subject", "filter", "ErrorSeverity", "ErrorService", "constructor", "errors$", "errorHistory$", "requestBlackList", "Set", "maxHistorySize", "addError", "error", "normalizedError", "normalizeError", "url", "isRequestBlackListed", "next", "addToHistory", "getErrors", "asObservable", "getErrorsByType", "type", "pipe", "getErrorsBySeverity", "severity", "getCustomErrors", "isCustomError", "getErrorHistory", "clearErrorHistory", "registerUnhandledRequestURL", "trim", "add", "removeFromBlacklist", "delete", "Array", "from", "some", "blacklistedUrl", "includes", "getBlacklistedUrls", "clearBlacklist", "clear", "createAccessDeniedError", "content", "id", "generateErrorId", "<PERSON><PERSON><PERSON>", "header", "timestamp", "Date", "statusCode", "HIGH", "createValidationError", "MEDIUM", "createSuccessMessage", "LOW", "createWarningMessage", "createInfoMessage", "createHttpError", "details", "errorInfo", "getHttpErrorInfo", "getHttpErrorSeverity", "isLegacyError", "determineSeverity", "status", "undefined", "message", "toString", "obj", "errorMap", "CRITICAL", "currentHistory", "value", "newHistory", "slice", "now", "Math", "random", "substr", "destroy", "complete", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\errors\\error.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, Subject } from 'rxjs';\r\nimport { filter } from 'rxjs/operators';\r\n\r\nexport interface AppError {\r\n  id: string;\r\n  errorKey?: number | string;\r\n  type: string;\r\n  header: string;\r\n  content: string;\r\n  isCustomError?: boolean;\r\n  timestamp: Date;\r\n  url?: string;\r\n  statusCode?: number;\r\n  details?: any;\r\n  severity?: ErrorSeverity;\r\n}\r\n\r\nexport interface LegacyErrorInput {\r\n  errorKey?: number | string;\r\n  type: string;\r\n  header: string;\r\n  content: string;\r\n  isCustomError?: boolean;\r\n  url?: string;\r\n  statusCode?: number;\r\n  details?: any;\r\n  severity?: ErrorSeverity;\r\n}\r\n\r\nexport enum ErrorSeverity {\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  CRITICAL = 'CRITICAL',\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ErrorService {\r\n  private readonly errors$ = new Subject<AppError>();\r\n  private readonly errorHistory$ = new BehaviorSubject<AppError[]>([]);\r\n  private readonly requestBlackList = new Set<string>();\r\n  private readonly maxHistorySize = 100;\r\n\r\n  /**\r\n   * Add error to the error stream\r\n   * @param error Error object with your custom structure or raw error data\r\n   */\r\n  public addError(error: LegacyErrorInput | any): void {\r\n    const normalizedError = this.normalizeError(error);\r\n\r\n    // Don't process blacklisted URLs\r\n    if (normalizedError.url && this.isRequestBlackListed(normalizedError.url)) {\r\n      return;\r\n    }\r\n\r\n    this.errors$.next(normalizedError);\r\n    this.addToHistory(normalizedError);\r\n  }\r\n\r\n  /**\r\n   * Get error observable stream\r\n   */\r\n  public getErrors(): Observable<AppError> {\r\n    return this.errors$.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Get errors filtered by type\r\n   */\r\n  public getErrorsByType(type: string): Observable<AppError> {\r\n    return this.errors$.pipe(filter((error) => error.type === type));\r\n  }\r\n\r\n  /**\r\n   * Get errors filtered by severity\r\n   */\r\n  public getErrorsBySeverity(severity: ErrorSeverity): Observable<AppError> {\r\n    return this.errors$.pipe(filter((error) => error.severity === severity));\r\n  }\r\n\r\n  /**\r\n   * Get custom errors only\r\n   */\r\n  public getCustomErrors(): Observable<AppError> {\r\n    return this.errors$.pipe(filter((error) => error.isCustomError === true));\r\n  }\r\n\r\n  /**\r\n   * Get error history\r\n   */\r\n  public getErrorHistory(): Observable<AppError[]> {\r\n    return this.errorHistory$.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Clear error history\r\n   */\r\n  public clearErrorHistory(): void {\r\n    this.errorHistory$.next([]);\r\n  }\r\n\r\n  /**\r\n   * Register URL to blacklist for error handling\r\n   * @param url URL pattern to ignore\r\n   */\r\n  public registerUnhandledRequestURL(url: string): void {\r\n    if (url && url.trim()) {\r\n      this.requestBlackList.add(url.trim());\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove URL from blacklist\r\n   * @param url URL pattern to remove from blacklist\r\n   */\r\n  public removeFromBlacklist(url: string): void {\r\n    this.requestBlackList.delete(url);\r\n  }\r\n\r\n  /**\r\n   * Check if request URL is blacklisted\r\n   * @param url URL to check\r\n   */\r\n  public isRequestBlackListed(url: string): boolean {\r\n    if (!url) return false;\r\n\r\n    return Array.from(this.requestBlackList).some((blacklistedUrl) =>\r\n      url.includes(blacklistedUrl)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get all blacklisted URLs\r\n   */\r\n  public getBlacklistedUrls(): string[] {\r\n    return Array.from(this.requestBlackList);\r\n  }\r\n\r\n  /**\r\n   * Clear all blacklisted URLs\r\n   */\r\n  public clearBlacklist(): void {\r\n    this.requestBlackList.clear();\r\n  }\r\n\r\n  /**\r\n   * Create a standardized access denied error\r\n   */\r\n  public createAccessDeniedError(content?: string): AppError {\r\n    return {\r\n      id: this.generateErrorId(),\r\n      errorKey: 403,\r\n      type: 'error',\r\n      header: 'Access Denied',\r\n      content: content || 'You do not have permission to access this resource.',\r\n      isCustomError: true,\r\n      timestamp: new Date(),\r\n      statusCode: 403,\r\n      severity: ErrorSeverity.HIGH,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a validation error\r\n   */\r\n  public createValidationError(\r\n    header: string,\r\n    content: string,\r\n    errorKey?: number | string\r\n  ): AppError {\r\n    return {\r\n      id: this.generateErrorId(),\r\n      errorKey: errorKey || 'validation',\r\n      type: 'validation',\r\n      header,\r\n      content,\r\n      isCustomError: true,\r\n      timestamp: new Date(),\r\n      severity: ErrorSeverity.MEDIUM,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a success message\r\n   */\r\n  public createSuccessMessage(header: string, content: string): AppError {\r\n    return {\r\n      id: this.generateErrorId(),\r\n      errorKey: 'success',\r\n      type: 'success',\r\n      header,\r\n      content,\r\n      isCustomError: true,\r\n      timestamp: new Date(),\r\n      severity: ErrorSeverity.LOW,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a warning message\r\n   */\r\n  public createWarningMessage(\r\n    header: string,\r\n    content: string,\r\n    errorKey?: number | string\r\n  ): AppError {\r\n    return {\r\n      id: this.generateErrorId(),\r\n      errorKey: errorKey || 'warning',\r\n      type: 'warning',\r\n      header,\r\n      content,\r\n      isCustomError: true,\r\n      timestamp: new Date(),\r\n      severity: ErrorSeverity.MEDIUM,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create an info message\r\n   */\r\n  public createInfoMessage(header: string, content: string): AppError {\r\n    return {\r\n      id: this.generateErrorId(),\r\n      errorKey: 'info',\r\n      type: 'info',\r\n      header,\r\n      content,\r\n      isCustomError: true,\r\n      timestamp: new Date(),\r\n      severity: ErrorSeverity.LOW,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create HTTP error from response\r\n   */\r\n  public createHttpError(\r\n    statusCode: number,\r\n    url?: string,\r\n    details?: any\r\n  ): AppError {\r\n    const errorInfo = this.getHttpErrorInfo(statusCode);\r\n\r\n    return {\r\n      id: this.generateErrorId(),\r\n      errorKey: statusCode,\r\n      type: 'error',\r\n      header: errorInfo.header,\r\n      content: errorInfo.content,\r\n      isCustomError: false,\r\n      timestamp: new Date(),\r\n      url,\r\n      statusCode,\r\n      details,\r\n      severity: this.getHttpErrorSeverity(statusCode),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Normalize different error formats into AppError\r\n   */\r\n  private normalizeError(error: any): AppError {\r\n    // If it's already in the expected format\r\n    if (this.isLegacyError(error)) {\r\n      return {\r\n        id: this.generateErrorId(),\r\n        timestamp: new Date(),\r\n        severity: this.determineSeverity(error),\r\n        ...error,\r\n      };\r\n    }\r\n\r\n    // Handle HTTP errors\r\n    if (error?.status !== undefined) {\r\n      return this.createHttpError(error.status, error.url, error);\r\n    }\r\n\r\n    // Handle Angular HttpErrorResponse\r\n    if (error?.error && error?.status) {\r\n      return this.createHttpError(error.status, error.url, error.error);\r\n    }\r\n\r\n    // Handle generic errors\r\n    return {\r\n      id: this.generateErrorId(),\r\n      errorKey: 'unknown',\r\n      type: 'error',\r\n      header: 'Unexpected Error',\r\n      content:\r\n        error?.message || error?.toString() || 'An unexpected error occurred',\r\n      isCustomError: false,\r\n      timestamp: new Date(),\r\n      details: error,\r\n      severity: ErrorSeverity.HIGH,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check if object matches your legacy error structure\r\n   */\r\n  private isLegacyError(obj: any): obj is LegacyErrorInput {\r\n    return (\r\n      obj &&\r\n      typeof obj.type === 'string' &&\r\n      typeof obj.header === 'string' &&\r\n      typeof obj.content === 'string'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Determine severity based on error properties\r\n   */\r\n  private determineSeverity(error: LegacyErrorInput): ErrorSeverity {\r\n    if (error.severity) return error.severity;\r\n\r\n    if (error.type === 'error') {\r\n      if (error.errorKey === 403 || error.errorKey === 401)\r\n        return ErrorSeverity.HIGH;\r\n      if (error.errorKey === 404) return ErrorSeverity.MEDIUM;\r\n      return ErrorSeverity.HIGH;\r\n    }\r\n\r\n    if (error.type === 'warning') return ErrorSeverity.MEDIUM;\r\n    if (error.type === 'success' || error.type === 'info')\r\n      return ErrorSeverity.LOW;\r\n\r\n    return ErrorSeverity.MEDIUM;\r\n  }\r\n\r\n  /**\r\n   * Get HTTP error information\r\n   */\r\n  private getHttpErrorInfo(statusCode: number): {\r\n    header: string;\r\n    content: string;\r\n  } {\r\n    const errorMap: { [key: number]: { header: string; content: string } } = {\r\n      400: {\r\n        header: 'Bad Request',\r\n        content: 'Invalid request. Please check your input and try again.',\r\n      },\r\n      401: {\r\n        header: 'Unauthorized',\r\n        content: 'You need to log in to access this resource.',\r\n      },\r\n      403: {\r\n        header: 'Access Denied',\r\n        content: 'You do not have permission to access this resource.',\r\n      },\r\n      404: {\r\n        header: 'Not Found',\r\n        content: 'The requested resource could not be found.',\r\n      },\r\n      409: {\r\n        header: 'Conflict',\r\n        content:\r\n          'A conflict occurred. The resource may have been modified by another user.',\r\n      },\r\n      422: {\r\n        header: 'Validation Error',\r\n        content: 'The provided data is invalid. Please check your input.',\r\n      },\r\n      429: {\r\n        header: 'Too Many Requests',\r\n        content: 'Too many requests. Please wait a moment and try again.',\r\n      },\r\n      500: {\r\n        header: 'Server Error',\r\n        content: 'An internal server error occurred. Please try again later.',\r\n      },\r\n      502: {\r\n        header: 'Service Unavailable',\r\n        content:\r\n          'The service is temporarily unavailable. Please try again later.',\r\n      },\r\n      503: {\r\n        header: 'Service Unavailable',\r\n        content:\r\n          'The service is temporarily unavailable. Please try again later.',\r\n      },\r\n      504: {\r\n        header: 'Request Timeout',\r\n        content: 'The request timed out. Please try again.',\r\n      },\r\n    };\r\n\r\n    return (\r\n      errorMap[statusCode] || {\r\n        header: 'Error',\r\n        content: `An error occurred (Status: ${statusCode}). Please try again.`,\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get severity for HTTP status codes\r\n   */\r\n  private getHttpErrorSeverity(statusCode: number): ErrorSeverity {\r\n    if (statusCode >= 500) return ErrorSeverity.CRITICAL;\r\n    if (statusCode === 403 || statusCode === 401) return ErrorSeverity.HIGH;\r\n    if (statusCode >= 400) return ErrorSeverity.MEDIUM;\r\n    return ErrorSeverity.LOW;\r\n  }\r\n\r\n  /**\r\n   * Add error to history with size limit\r\n   */\r\n  private addToHistory(error: AppError): void {\r\n    const currentHistory = this.errorHistory$.value;\r\n    const newHistory = [error, ...currentHistory].slice(0, this.maxHistorySize);\r\n    this.errorHistory$.next(newHistory);\r\n  }\r\n\r\n  /**\r\n   * Generate unique error ID\r\n   */\r\n  private generateErrorId(): string {\r\n    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  /**\r\n   * Clean up resources\r\n   */\r\n  public destroy(): void {\r\n    this.errors$.complete();\r\n    this.errorHistory$.complete();\r\n    this.requestBlackList.clear();\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,EAAcC,OAAO,QAAQ,MAAM;AAC3D,SAASC,MAAM,QAAQ,gBAAgB;;AA4BvC,WAAYC,aAKX;AALD,WAAYA,aAAa;EACvBA,aAAA,eAAW;EACXA,aAAA,qBAAiB;EACjBA,aAAA,iBAAa;EACbA,aAAA,yBAAqB;AACvB,CAAC,EALWA,aAAa,KAAbA,aAAa;AAUzB,OAAM,MAAOC,YAAY;EAHzBC,YAAA;IAImB,KAAAC,OAAO,GAAG,IAAIL,OAAO,EAAY;IACjC,KAAAM,aAAa,GAAG,IAAIP,eAAe,CAAa,EAAE,CAAC;IACnD,KAAAQ,gBAAgB,GAAG,IAAIC,GAAG,EAAU;IACpC,KAAAC,cAAc,GAAG,GAAG;;EAErC;;;;EAIOC,QAAQA,CAACC,KAA6B;IAC3C,MAAMC,eAAe,GAAG,IAAI,CAACC,cAAc,CAACF,KAAK,CAAC;IAElD;IACA,IAAIC,eAAe,CAACE,GAAG,IAAI,IAAI,CAACC,oBAAoB,CAACH,eAAe,CAACE,GAAG,CAAC,EAAE;MACzE;;IAGF,IAAI,CAACT,OAAO,CAACW,IAAI,CAACJ,eAAe,CAAC;IAClC,IAAI,CAACK,YAAY,CAACL,eAAe,CAAC;EACpC;EAEA;;;EAGOM,SAASA,CAAA;IACd,OAAO,IAAI,CAACb,OAAO,CAACc,YAAY,EAAE;EACpC;EAEA;;;EAGOC,eAAeA,CAACC,IAAY;IACjC,OAAO,IAAI,CAAChB,OAAO,CAACiB,IAAI,CAACrB,MAAM,CAAEU,KAAK,IAAKA,KAAK,CAACU,IAAI,KAAKA,IAAI,CAAC,CAAC;EAClE;EAEA;;;EAGOE,mBAAmBA,CAACC,QAAuB;IAChD,OAAO,IAAI,CAACnB,OAAO,CAACiB,IAAI,CAACrB,MAAM,CAAEU,KAAK,IAAKA,KAAK,CAACa,QAAQ,KAAKA,QAAQ,CAAC,CAAC;EAC1E;EAEA;;;EAGOC,eAAeA,CAAA;IACpB,OAAO,IAAI,CAACpB,OAAO,CAACiB,IAAI,CAACrB,MAAM,CAAEU,KAAK,IAAKA,KAAK,CAACe,aAAa,KAAK,IAAI,CAAC,CAAC;EAC3E;EAEA;;;EAGOC,eAAeA,CAAA;IACpB,OAAO,IAAI,CAACrB,aAAa,CAACa,YAAY,EAAE;EAC1C;EAEA;;;EAGOS,iBAAiBA,CAAA;IACtB,IAAI,CAACtB,aAAa,CAACU,IAAI,CAAC,EAAE,CAAC;EAC7B;EAEA;;;;EAIOa,2BAA2BA,CAACf,GAAW;IAC5C,IAAIA,GAAG,IAAIA,GAAG,CAACgB,IAAI,EAAE,EAAE;MACrB,IAAI,CAACvB,gBAAgB,CAACwB,GAAG,CAACjB,GAAG,CAACgB,IAAI,EAAE,CAAC;;EAEzC;EAEA;;;;EAIOE,mBAAmBA,CAAClB,GAAW;IACpC,IAAI,CAACP,gBAAgB,CAAC0B,MAAM,CAACnB,GAAG,CAAC;EACnC;EAEA;;;;EAIOC,oBAAoBA,CAACD,GAAW;IACrC,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;IAEtB,OAAOoB,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,CAAC,CAAC6B,IAAI,CAAEC,cAAc,IAC3DvB,GAAG,CAACwB,QAAQ,CAACD,cAAc,CAAC,CAC7B;EACH;EAEA;;;EAGOE,kBAAkBA,CAAA;IACvB,OAAOL,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,CAAC;EAC1C;EAEA;;;EAGOiC,cAAcA,CAAA;IACnB,IAAI,CAACjC,gBAAgB,CAACkC,KAAK,EAAE;EAC/B;EAEA;;;EAGOC,uBAAuBA,CAACC,OAAgB;IAC7C,OAAO;MACLC,EAAE,EAAE,IAAI,CAACC,eAAe,EAAE;MAC1BC,QAAQ,EAAE,GAAG;MACbzB,IAAI,EAAE,OAAO;MACb0B,MAAM,EAAE,eAAe;MACvBJ,OAAO,EAAEA,OAAO,IAAI,qDAAqD;MACzEjB,aAAa,EAAE,IAAI;MACnBsB,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,UAAU,EAAE,GAAG;MACf1B,QAAQ,EAAEtB,aAAa,CAACiD;KACzB;EACH;EAEA;;;EAGOC,qBAAqBA,CAC1BL,MAAc,EACdJ,OAAe,EACfG,QAA0B;IAE1B,OAAO;MACLF,EAAE,EAAE,IAAI,CAACC,eAAe,EAAE;MAC1BC,QAAQ,EAAEA,QAAQ,IAAI,YAAY;MAClCzB,IAAI,EAAE,YAAY;MAClB0B,MAAM;MACNJ,OAAO;MACPjB,aAAa,EAAE,IAAI;MACnBsB,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBzB,QAAQ,EAAEtB,aAAa,CAACmD;KACzB;EACH;EAEA;;;EAGOC,oBAAoBA,CAACP,MAAc,EAAEJ,OAAe;IACzD,OAAO;MACLC,EAAE,EAAE,IAAI,CAACC,eAAe,EAAE;MAC1BC,QAAQ,EAAE,SAAS;MACnBzB,IAAI,EAAE,SAAS;MACf0B,MAAM;MACNJ,OAAO;MACPjB,aAAa,EAAE,IAAI;MACnBsB,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBzB,QAAQ,EAAEtB,aAAa,CAACqD;KACzB;EACH;EAEA;;;EAGOC,oBAAoBA,CACzBT,MAAc,EACdJ,OAAe,EACfG,QAA0B;IAE1B,OAAO;MACLF,EAAE,EAAE,IAAI,CAACC,eAAe,EAAE;MAC1BC,QAAQ,EAAEA,QAAQ,IAAI,SAAS;MAC/BzB,IAAI,EAAE,SAAS;MACf0B,MAAM;MACNJ,OAAO;MACPjB,aAAa,EAAE,IAAI;MACnBsB,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBzB,QAAQ,EAAEtB,aAAa,CAACmD;KACzB;EACH;EAEA;;;EAGOI,iBAAiBA,CAACV,MAAc,EAAEJ,OAAe;IACtD,OAAO;MACLC,EAAE,EAAE,IAAI,CAACC,eAAe,EAAE;MAC1BC,QAAQ,EAAE,MAAM;MAChBzB,IAAI,EAAE,MAAM;MACZ0B,MAAM;MACNJ,OAAO;MACPjB,aAAa,EAAE,IAAI;MACnBsB,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBzB,QAAQ,EAAEtB,aAAa,CAACqD;KACzB;EACH;EAEA;;;EAGOG,eAAeA,CACpBR,UAAkB,EAClBpC,GAAY,EACZ6C,OAAa;IAEb,MAAMC,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAACX,UAAU,CAAC;IAEnD,OAAO;MACLN,EAAE,EAAE,IAAI,CAACC,eAAe,EAAE;MAC1BC,QAAQ,EAAEI,UAAU;MACpB7B,IAAI,EAAE,OAAO;MACb0B,MAAM,EAAEa,SAAS,CAACb,MAAM;MACxBJ,OAAO,EAAEiB,SAAS,CAACjB,OAAO;MAC1BjB,aAAa,EAAE,KAAK;MACpBsB,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBnC,GAAG;MACHoC,UAAU;MACVS,OAAO;MACPnC,QAAQ,EAAE,IAAI,CAACsC,oBAAoB,CAACZ,UAAU;KAC/C;EACH;EAEA;;;EAGQrC,cAAcA,CAACF,KAAU;IAC/B;IACA,IAAI,IAAI,CAACoD,aAAa,CAACpD,KAAK,CAAC,EAAE;MAC7B,OAAO;QACLiC,EAAE,EAAE,IAAI,CAACC,eAAe,EAAE;QAC1BG,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBzB,QAAQ,EAAE,IAAI,CAACwC,iBAAiB,CAACrD,KAAK,CAAC;QACvC,GAAGA;OACJ;;IAGH;IACA,IAAIA,KAAK,EAAEsD,MAAM,KAAKC,SAAS,EAAE;MAC/B,OAAO,IAAI,CAACR,eAAe,CAAC/C,KAAK,CAACsD,MAAM,EAAEtD,KAAK,CAACG,GAAG,EAAEH,KAAK,CAAC;;IAG7D;IACA,IAAIA,KAAK,EAAEA,KAAK,IAAIA,KAAK,EAAEsD,MAAM,EAAE;MACjC,OAAO,IAAI,CAACP,eAAe,CAAC/C,KAAK,CAACsD,MAAM,EAAEtD,KAAK,CAACG,GAAG,EAAEH,KAAK,CAACA,KAAK,CAAC;;IAGnE;IACA,OAAO;MACLiC,EAAE,EAAE,IAAI,CAACC,eAAe,EAAE;MAC1BC,QAAQ,EAAE,SAAS;MACnBzB,IAAI,EAAE,OAAO;MACb0B,MAAM,EAAE,kBAAkB;MAC1BJ,OAAO,EACLhC,KAAK,EAAEwD,OAAO,IAAIxD,KAAK,EAAEyD,QAAQ,EAAE,IAAI,8BAA8B;MACvE1C,aAAa,EAAE,KAAK;MACpBsB,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBU,OAAO,EAAEhD,KAAK;MACda,QAAQ,EAAEtB,aAAa,CAACiD;KACzB;EACH;EAEA;;;EAGQY,aAAaA,CAACM,GAAQ;IAC5B,OACEA,GAAG,IACH,OAAOA,GAAG,CAAChD,IAAI,KAAK,QAAQ,IAC5B,OAAOgD,GAAG,CAACtB,MAAM,KAAK,QAAQ,IAC9B,OAAOsB,GAAG,CAAC1B,OAAO,KAAK,QAAQ;EAEnC;EAEA;;;EAGQqB,iBAAiBA,CAACrD,KAAuB;IAC/C,IAAIA,KAAK,CAACa,QAAQ,EAAE,OAAOb,KAAK,CAACa,QAAQ;IAEzC,IAAIb,KAAK,CAACU,IAAI,KAAK,OAAO,EAAE;MAC1B,IAAIV,KAAK,CAACmC,QAAQ,KAAK,GAAG,IAAInC,KAAK,CAACmC,QAAQ,KAAK,GAAG,EAClD,OAAO5C,aAAa,CAACiD,IAAI;MAC3B,IAAIxC,KAAK,CAACmC,QAAQ,KAAK,GAAG,EAAE,OAAO5C,aAAa,CAACmD,MAAM;MACvD,OAAOnD,aAAa,CAACiD,IAAI;;IAG3B,IAAIxC,KAAK,CAACU,IAAI,KAAK,SAAS,EAAE,OAAOnB,aAAa,CAACmD,MAAM;IACzD,IAAI1C,KAAK,CAACU,IAAI,KAAK,SAAS,IAAIV,KAAK,CAACU,IAAI,KAAK,MAAM,EACnD,OAAOnB,aAAa,CAACqD,GAAG;IAE1B,OAAOrD,aAAa,CAACmD,MAAM;EAC7B;EAEA;;;EAGQQ,gBAAgBA,CAACX,UAAkB;IAIzC,MAAMoB,QAAQ,GAA2D;MACvE,GAAG,EAAE;QACHvB,MAAM,EAAE,aAAa;QACrBJ,OAAO,EAAE;OACV;MACD,GAAG,EAAE;QACHI,MAAM,EAAE,cAAc;QACtBJ,OAAO,EAAE;OACV;MACD,GAAG,EAAE;QACHI,MAAM,EAAE,eAAe;QACvBJ,OAAO,EAAE;OACV;MACD,GAAG,EAAE;QACHI,MAAM,EAAE,WAAW;QACnBJ,OAAO,EAAE;OACV;MACD,GAAG,EAAE;QACHI,MAAM,EAAE,UAAU;QAClBJ,OAAO,EACL;OACH;MACD,GAAG,EAAE;QACHI,MAAM,EAAE,kBAAkB;QAC1BJ,OAAO,EAAE;OACV;MACD,GAAG,EAAE;QACHI,MAAM,EAAE,mBAAmB;QAC3BJ,OAAO,EAAE;OACV;MACD,GAAG,EAAE;QACHI,MAAM,EAAE,cAAc;QACtBJ,OAAO,EAAE;OACV;MACD,GAAG,EAAE;QACHI,MAAM,EAAE,qBAAqB;QAC7BJ,OAAO,EACL;OACH;MACD,GAAG,EAAE;QACHI,MAAM,EAAE,qBAAqB;QAC7BJ,OAAO,EACL;OACH;MACD,GAAG,EAAE;QACHI,MAAM,EAAE,iBAAiB;QACzBJ,OAAO,EAAE;;KAEZ;IAED,OACE2B,QAAQ,CAACpB,UAAU,CAAC,IAAI;MACtBH,MAAM,EAAE,OAAO;MACfJ,OAAO,EAAE,8BAA8BO,UAAU;KAClD;EAEL;EAEA;;;EAGQY,oBAAoBA,CAACZ,UAAkB;IAC7C,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAOhD,aAAa,CAACqE,QAAQ;IACpD,IAAIrB,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,GAAG,EAAE,OAAOhD,aAAa,CAACiD,IAAI;IACvE,IAAID,UAAU,IAAI,GAAG,EAAE,OAAOhD,aAAa,CAACmD,MAAM;IAClD,OAAOnD,aAAa,CAACqD,GAAG;EAC1B;EAEA;;;EAGQtC,YAAYA,CAACN,KAAe;IAClC,MAAM6D,cAAc,GAAG,IAAI,CAAClE,aAAa,CAACmE,KAAK;IAC/C,MAAMC,UAAU,GAAG,CAAC/D,KAAK,EAAE,GAAG6D,cAAc,CAAC,CAACG,KAAK,CAAC,CAAC,EAAE,IAAI,CAAClE,cAAc,CAAC;IAC3E,IAAI,CAACH,aAAa,CAACU,IAAI,CAAC0D,UAAU,CAAC;EACrC;EAEA;;;EAGQ7B,eAAeA,CAAA;IACrB,OAAO,SAASI,IAAI,CAAC2B,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAACV,QAAQ,CAAC,EAAE,CAAC,CAACW,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACzE;EAEA;;;EAGOC,OAAOA,CAAA;IACZ,IAAI,CAAC3E,OAAO,CAAC4E,QAAQ,EAAE;IACvB,IAAI,CAAC3E,aAAa,CAAC2E,QAAQ,EAAE;IAC7B,IAAI,CAAC1E,gBAAgB,CAACkC,KAAK,EAAE;EAC/B;EAAC,QAAAyC,CAAA,G;qBAvYU/E,YAAY;EAAA;EAAA,QAAAgF,EAAA,G;WAAZhF,YAAY;IAAAiF,OAAA,EAAZjF,YAAY,CAAAkF,IAAA;IAAAC,UAAA,EAFX;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}