{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AuthParserService {\n  constructor() {}\n  intercept(request, next) {\n    request = request.clone({\n      withCredentials: true\n      // setHeaders: {\n      //   Authorization: environment.bearerToken,\n      // },\n    });\n    return next.handle(request);\n  }\n  static #_ = this.ɵfac = function AuthParserService_Factory(t) {\n    return new (t || AuthParserService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthParserService,\n    factory: AuthParserService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["AuthParserService", "constructor", "intercept", "request", "next", "clone", "withCredentials", "handle", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\interceptors\\auth-parser.interceptor.ts"], "sourcesContent": ["import {\r\n  <PERSON>ttp<PERSON><PERSON>,\r\n  <PERSON>ttp<PERSON><PERSON><PERSON>,\r\n  HttpInterceptor,\r\n  HttpRequest,\r\n} from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthParserService implements HttpInterceptor {\r\n  constructor() {}\r\n\r\n  intercept(\r\n    request: HttpRequest<any>,\r\n    next: <PERSON>ttpHandler\r\n  ): Observable<HttpEvent<any>> {\r\n    request = request.clone({\r\n      withCredentials: true,\r\n      // setHeaders: {\r\n      //   Authorization: environment.bearerToken,\r\n      // },\r\n    });\r\n    return next.handle(request);\r\n  }\r\n}\r\n"], "mappings": ";AAYA,OAAM,MAAOA,iBAAiB;EAC5BC,YAAA,GAAe;EAEfC,SAASA,CACPC,OAAyB,EACzBC,IAAiB;IAEjBD,OAAO,GAAGA,OAAO,CAACE,KAAK,CAAC;MACtBC,eAAe,EAAE;MACjB;MACA;MACA;KACD,CAAC;IACF,OAAOF,IAAI,CAACG,MAAM,CAACJ,OAAO,CAAC;EAC7B;EAAC,QAAAK,CAAA,G;qBAdUR,iBAAiB;EAAA;EAAA,QAAAS,EAAA,G;WAAjBT,iBAAiB;IAAAU,OAAA,EAAjBV,iBAAiB,CAAAW,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}