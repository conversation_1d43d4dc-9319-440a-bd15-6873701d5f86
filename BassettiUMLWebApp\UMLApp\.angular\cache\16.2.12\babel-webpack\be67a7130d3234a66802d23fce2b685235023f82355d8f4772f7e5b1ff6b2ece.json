{"ast": null, "code": "import { BehaviorSubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, filter as rxFilter, takeUntil } from 'rxjs/operators';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\n/**\n * Error severity levels\n */\nexport var ErrorSeverity;\n(function (ErrorSeverity) {\n  ErrorSeverity[\"LOW\"] = \"low\";\n  ErrorSeverity[\"MEDIUM\"] = \"medium\";\n  ErrorSeverity[\"HIGH\"] = \"high\";\n  ErrorSeverity[\"CRITICAL\"] = \"critical\";\n})(ErrorSeverity || (ErrorSeverity = {}));\n/**\n * Error categories for better organization\n */\nexport var ErrorCategory;\n(function (ErrorCategory) {\n  ErrorCategory[\"NETWORK\"] = \"network\";\n  ErrorCategory[\"AUTHENTICATION\"] = \"authentication\";\n  ErrorCategory[\"AUTHORIZATION\"] = \"authorization\";\n  ErrorCategory[\"VALIDATION\"] = \"validation\";\n  ErrorCategory[\"BUSINESS_LOGIC\"] = \"business_logic\";\n  ErrorCategory[\"SYSTEM\"] = \"system\";\n  ErrorCategory[\"USER_INPUT\"] = \"user_input\";\n  ErrorCategory[\"EXTERNAL_SERVICE\"] = \"external_service\";\n})(ErrorCategory || (ErrorCategory = {}));\nexport class ErrorService {\n  constructor() {\n    this.destroy$ = new Subject();\n    this.errors$ = new Subject();\n    this.legacyErrors$ = new Subject(); // For backward compatibility\n    this.errorHistory$ = new BehaviorSubject([]);\n    this.statistics$ = new BehaviorSubject(this.initializeStatistics());\n    this.requestBlackList = [];\n    this.errorFilters = [];\n    this.errorHistory = [];\n    this.config = {\n      enableLogging: !environment.production,\n      enableAnalytics: true,\n      maxErrorHistory: 100,\n      debounceTime: 300,\n      defaultRetryAttempts: 3,\n      logLevel: environment.production ? 'error' : 'debug'\n    };\n    this.initializeErrorHandling();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.errors$.complete();\n    this.legacyErrors$.complete();\n    this.errorHistory$.complete();\n    this.statistics$.complete();\n  }\n  /**\n   * Initialize error handling pipeline\n   */\n  initializeErrorHandling() {\n    this.errors$.pipe(takeUntil(this.destroy$), debounceTime(this.config.debounceTime || 300), distinctUntilChanged((prev, curr) => prev.message === curr.message && prev.statusCode === curr.statusCode && Math.abs(curr.timestamp.getTime() - prev.timestamp.getTime()) < 5000)).subscribe(error => {\n      this.processError(error);\n    });\n  }\n  /**\n   * Add error with enhanced metadata and processing\n   */\n  addError(error, context) {\n    const enhancedError = this.createEnhancedError(error, context);\n    if (this.shouldSuppressError(enhancedError)) {\n      return enhancedError.id;\n    }\n    this.errors$.next(enhancedError);\n    return enhancedError.id;\n  }\n  /**\n   * Add custom error with specific configuration\n   */\n  addCustomError(message, category = ErrorCategory.SYSTEM, severity = ErrorSeverity.MEDIUM, context, options) {\n    const customError = {\n      isCustomError: true,\n      message,\n      category,\n      severity,\n      error: {\n        errorKey: options?.errorKey,\n        message\n      },\n      ...options\n    };\n    return this.addError(customError, {\n      ...context,\n      component: context?.component || 'custom'\n    });\n  }\n  /**\n   * Get errors observable with optional filtering\n   */\n  getErrors(errorFilter) {\n    return this.errors$.pipe(rxFilter(error => this.matchesFilter(error, errorFilter)), takeUntil(this.destroy$));\n  }\n  /**\n   * Get errors observable (backward compatibility - returns legacy format)\n   * @deprecated Use getErrors() instead for enhanced error handling\n   */\n  getErrorsLegacy() {\n    return this.legacyErrors$.pipe(takeUntil(this.destroy$));\n  }\n  /**\n   * Get error history\n   */\n  getErrorHistory() {\n    return this.errorHistory$.asObservable();\n  }\n  /**\n   * Get error statistics\n   */\n  getStatistics() {\n    return this.statistics$.asObservable();\n  }\n  /**\n   * Register unhandled URL to the blacklist (backward compatibility)\n   */\n  registerUnhandledRequestURL(url) {\n    this.requestBlackList.push(url);\n  }\n  /**\n   * Check if request URL is blacklisted (backward compatibility)\n   */\n  isRequestBlackListed(url) {\n    return this.requestBlackList.some(rq => url.includes(rq));\n  }\n  /**\n   * Add error filter\n   */\n  addErrorFilter(filter) {\n    this.errorFilters.push(filter);\n  }\n  /**\n   * Remove error filter\n   */\n  removeErrorFilter(filter) {\n    const index = this.errorFilters.indexOf(filter);\n    if (index > -1) {\n      this.errorFilters.splice(index, 1);\n    }\n  }\n  /**\n   * Clear all error filters\n   */\n  clearErrorFilters() {\n    this.errorFilters = [];\n  }\n  /**\n   * Update service configuration\n   */\n  updateConfig(config) {\n    this.config = {\n      ...this.config,\n      ...config\n    };\n  }\n  /**\n   * Clear error history\n   */\n  clearErrorHistory() {\n    this.errorHistory = [];\n    this.errorHistory$.next([]);\n  }\n  /**\n   * Mark error as handled\n   */\n  markErrorAsHandled(errorId) {\n    const error = this.errorHistory.find(e => e.id === errorId);\n    if (error) {\n      error.handled = true;\n      this.errorHistory$.next([...this.errorHistory]);\n    }\n  }\n  /**\n   * Retry failed operation\n   */\n  retryError(errorId, retryFunction) {\n    const error = this.errorHistory.find(e => e.id === errorId);\n    if (!error || !error.retryable) {\n      throw new Error('Error is not retryable or not found');\n    }\n    error.retryCount = (error.retryCount || 0) + 1;\n    if (error.retryCount > (error.maxRetries || this.config.defaultRetryAttempts || 3)) {\n      throw new Error('Maximum retry attempts exceeded');\n    }\n    return retryFunction();\n  }\n  /**\n   * Initialize statistics object\n   */\n  initializeStatistics() {\n    return {\n      totalErrors: 0,\n      errorsByCategory: {},\n      errorsBySeverity: {},\n      errorsByStatusCode: {},\n      mostCommonErrors: [],\n      errorTrends: []\n    };\n  }\n  /**\n   * Create enhanced error from raw error\n   */\n  createEnhancedError(error, context) {\n    const timestamp = new Date();\n    const id = this.generateErrorId();\n    // Extract error information\n    const isHttpError = error?.status !== undefined;\n    const isCustomError = error?.isCustomError === true;\n    let message = '';\n    let statusCode;\n    let errorKey;\n    let category = ErrorCategory.SYSTEM;\n    let severity = ErrorSeverity.MEDIUM;\n    if (isCustomError) {\n      message = error.message || 'Custom error occurred';\n      errorKey = error.error?.errorKey;\n      category = this.determineCategory(error);\n      severity = this.determineSeverity(error);\n    } else if (isHttpError) {\n      statusCode = error.status;\n      message = error.error?.message || error.message || `HTTP ${statusCode} Error`;\n      errorKey = error.error?.errorKey;\n      category = this.categorizeHttpError(statusCode || 0);\n      severity = this.determineSeverityFromStatus(statusCode || 0);\n    } else {\n      message = error?.message || 'Unknown error occurred';\n      category = ErrorCategory.SYSTEM;\n      severity = ErrorSeverity.HIGH;\n    }\n    const enhancedError = {\n      id,\n      originalError: error,\n      message,\n      severity,\n      category,\n      context: {\n        timestamp,\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n        ...context\n      },\n      isCustomError,\n      errorKey,\n      statusCode,\n      retryable: this.isRetryable(error, statusCode),\n      retryCount: 0,\n      maxRetries: this.config.defaultRetryAttempts,\n      suppressDialog: error?.suppressDialog || false,\n      handled: false,\n      timestamp\n    };\n    return enhancedError;\n  }\n  /**\n   * Process error through the pipeline\n   */\n  processError(error) {\n    // Add to history\n    this.addToHistory(error);\n    // Update statistics\n    this.updateStatistics(error);\n    // Log error if enabled\n    if (this.config.enableLogging && !this.shouldSuppressLogging(error)) {\n      this.logError(error);\n    }\n    // Emit for dialog display (backward compatibility)\n    if (!error.suppressDialog) {\n      // Convert back to original format for backward compatibility\n      const legacyError = this.convertToLegacyFormat(error);\n      this.legacyErrors$.next(legacyError);\n      console.debug('Legacy error format for dialog:', legacyError);\n    }\n  }\n  /**\n   * Check if error should be suppressed\n   */\n  shouldSuppressError(error) {\n    // Check URL blacklist\n    if (error.context.url && this.isRequestBlackListed(error.context.url)) {\n      return true;\n    }\n    // Check error filters\n    return this.errorFilters.some(filter => this.matchesFilter(error, filter));\n  }\n  /**\n   * Check if error matches filter\n   */\n  matchesFilter(error, filter) {\n    if (!filter) return true;\n    if (filter.statusCodes && error.statusCode && !filter.statusCodes.includes(error.statusCode)) {\n      return false;\n    }\n    if (filter.errorKeys && error.errorKey && !filter.errorKeys.includes(error.errorKey)) {\n      return false;\n    }\n    if (filter.categories && !filter.categories.includes(error.category)) {\n      return false;\n    }\n    if (filter.severities && !filter.severities.includes(error.severity)) {\n      return false;\n    }\n    if (filter.urlPatterns && error.context.url) {\n      const matchesUrl = filter.urlPatterns.some(pattern => error.context.url.includes(pattern));\n      if (!matchesUrl) return false;\n    }\n    return true;\n  }\n  /**\n   * Check if logging should be suppressed for this error\n   */\n  shouldSuppressLogging(error) {\n    return this.errorFilters.some(filter => filter.suppressLogging && this.matchesFilter(error, filter));\n  }\n  /**\n   * Add error to history with size management\n   */\n  addToHistory(error) {\n    this.errorHistory.unshift(error);\n    // Maintain max history size\n    if (this.errorHistory.length > (this.config.maxErrorHistory || 100)) {\n      this.errorHistory = this.errorHistory.slice(0, this.config.maxErrorHistory || 100);\n    }\n    this.errorHistory$.next([...this.errorHistory]);\n  }\n  /**\n   * Update error statistics\n   */\n  updateStatistics(error) {\n    const currentStats = this.statistics$.value;\n    // Update total count\n    currentStats.totalErrors++;\n    // Update category count\n    currentStats.errorsByCategory[error.category] = (currentStats.errorsByCategory[error.category] || 0) + 1;\n    // Update severity count\n    currentStats.errorsBySeverity[error.severity] = (currentStats.errorsBySeverity[error.severity] || 0) + 1;\n    // Update status code count\n    if (error.statusCode) {\n      currentStats.errorsByStatusCode[error.statusCode] = (currentStats.errorsByStatusCode[error.statusCode] || 0) + 1;\n    }\n    // Update most common errors\n    if (error.errorKey) {\n      const existingError = currentStats.mostCommonErrors.find(e => e.errorKey === error.errorKey);\n      if (existingError) {\n        existingError.count++;\n      } else {\n        currentStats.mostCommonErrors.push({\n          errorKey: error.errorKey,\n          count: 1\n        });\n      }\n      // Sort and keep top 10\n      currentStats.mostCommonErrors.sort((a, b) => b.count - a.count);\n      currentStats.mostCommonErrors = currentStats.mostCommonErrors.slice(0, 10);\n    }\n    // Update trends (hourly buckets)\n    const hourBucket = new Date(error.timestamp);\n    hourBucket.setMinutes(0, 0, 0);\n    const existingTrend = currentStats.errorTrends.find(t => t.timestamp.getTime() === hourBucket.getTime());\n    if (existingTrend) {\n      existingTrend.count++;\n    } else {\n      currentStats.errorTrends.push({\n        timestamp: hourBucket,\n        count: 1\n      });\n    }\n    // Keep only last 24 hours\n    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\n    currentStats.errorTrends = currentStats.errorTrends.filter(t => t.timestamp >= oneDayAgo);\n    this.statistics$.next(currentStats);\n  }\n  /**\n   * Log error based on configuration\n   */\n  logError(error) {\n    const logLevel = this.config.logLevel || 'error';\n    const logMessage = `[${error.severity.toUpperCase()}] ${error.category}: ${error.message}`;\n    const logData = {\n      id: error.id,\n      category: error.category,\n      severity: error.severity,\n      statusCode: error.statusCode,\n      errorKey: error.errorKey,\n      context: error.context,\n      originalError: error.originalError\n    };\n    switch (logLevel) {\n      case 'debug':\n        console.debug(logMessage, logData);\n        break;\n      case 'info':\n        if (error.severity === ErrorSeverity.LOW) {\n          console.info(logMessage, logData);\n        } else {\n          console.error(logMessage, logData);\n        }\n        break;\n      case 'warn':\n        if (error.severity === ErrorSeverity.LOW || error.severity === ErrorSeverity.MEDIUM) {\n          console.warn(logMessage, logData);\n        } else {\n          console.error(logMessage, logData);\n        }\n        break;\n      case 'error':\n      default:\n        console.error(logMessage, logData);\n        break;\n    }\n  }\n  /**\n   * Generate unique error ID\n   */\n  generateErrorId() {\n    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n  }\n  /**\n   * Determine error category from custom error\n   */\n  determineCategory(error) {\n    const errorKey = error.error?.errorKey?.toLowerCase() || '';\n    if (errorKey.includes('auth') || errorKey.includes('login') || errorKey.includes('token')) {\n      return ErrorCategory.AUTHENTICATION;\n    }\n    if (errorKey.includes('permission') || errorKey.includes('access') || errorKey.includes('forbidden')) {\n      return ErrorCategory.AUTHORIZATION;\n    }\n    if (errorKey.includes('validation') || errorKey.includes('invalid') || errorKey.includes('required')) {\n      return ErrorCategory.VALIDATION;\n    }\n    if (errorKey.includes('business') || errorKey.includes('rule') || errorKey.includes('logic')) {\n      return ErrorCategory.BUSINESS_LOGIC;\n    }\n    if (errorKey.includes('input') || errorKey.includes('user')) {\n      return ErrorCategory.USER_INPUT;\n    }\n    if (errorKey.includes('external') || errorKey.includes('service') || errorKey.includes('api')) {\n      return ErrorCategory.EXTERNAL_SERVICE;\n    }\n    return ErrorCategory.SYSTEM;\n  }\n  /**\n   * Determine error severity from custom error\n   */\n  determineSeverity(error) {\n    const errorKey = error.error?.errorKey?.toLowerCase() || '';\n    if (errorKey.includes('critical') || errorKey.includes('fatal')) {\n      return ErrorSeverity.CRITICAL;\n    }\n    if (errorKey.includes('high') || errorKey.includes('severe')) {\n      return ErrorSeverity.HIGH;\n    }\n    if (errorKey.includes('low') || errorKey.includes('minor')) {\n      return ErrorSeverity.LOW;\n    }\n    return ErrorSeverity.MEDIUM;\n  }\n  /**\n   * Categorize HTTP error by status code\n   */\n  categorizeHttpError(statusCode) {\n    if (statusCode === 401) {\n      return ErrorCategory.AUTHENTICATION;\n    }\n    if (statusCode === 403) {\n      return ErrorCategory.AUTHORIZATION;\n    }\n    if (statusCode >= 400 && statusCode < 500) {\n      return ErrorCategory.VALIDATION;\n    }\n    if (statusCode >= 500) {\n      return ErrorCategory.SYSTEM;\n    }\n    if (statusCode === 0 || statusCode < 0) {\n      return ErrorCategory.NETWORK;\n    }\n    return ErrorCategory.SYSTEM;\n  }\n  /**\n   * Determine severity from HTTP status code\n   */\n  determineSeverityFromStatus(statusCode) {\n    if (statusCode >= 500) {\n      return ErrorSeverity.HIGH;\n    }\n    if (statusCode === 401 || statusCode === 403) {\n      return ErrorSeverity.MEDIUM;\n    }\n    if (statusCode === 404) {\n      return ErrorSeverity.LOW;\n    }\n    if (statusCode >= 400 && statusCode < 500) {\n      return ErrorSeverity.MEDIUM;\n    }\n    if (statusCode === 0 || statusCode < 0) {\n      return ErrorSeverity.HIGH;\n    }\n    return ErrorSeverity.MEDIUM;\n  }\n  /**\n   * Check if error is retryable\n   */\n  isRetryable(_error, statusCode) {\n    // Network errors are usually retryable\n    if (statusCode === 0 || statusCode === undefined) {\n      return true;\n    }\n    // Server errors are retryable\n    if (statusCode >= 500) {\n      return true;\n    }\n    // Timeout errors are retryable\n    if (statusCode === 408 || statusCode === 504) {\n      return true;\n    }\n    // Rate limiting is retryable\n    if (statusCode === 429) {\n      return true;\n    }\n    // Client errors are generally not retryable\n    if (statusCode >= 400 && statusCode < 500) {\n      return false;\n    }\n    return false;\n  }\n  /**\n   * Convert enhanced error back to legacy format for backward compatibility\n   */\n  convertToLegacyFormat(error) {\n    return {\n      ...error.originalError,\n      status: error.statusCode,\n      error: {\n        errorKey: error.errorKey,\n        message: error.message,\n        errorDetails: error.context.additionalData\n      },\n      isCustomError: error.isCustomError\n    };\n  }\n  static #_ = this.ɵfac = function ErrorService_Factory(t) {\n    return new (t || ErrorService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ErrorService,\n    factory: ErrorService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Subject", "debounceTime", "distinctUntilChanged", "filter", "rxFilter", "takeUntil", "environment", "ErrorSeverity", "Error<PERSON>ate<PERSON><PERSON>", "ErrorService", "constructor", "destroy$", "errors$", "legacyErrors$", "errorHistory$", "statistics$", "initializeStatistics", "requestBlackList", "errorFilters", "errorHistory", "config", "enableLogging", "production", "enableAnalytics", "maxError<PERSON><PERSON>ory", "defaultRetryAttempts", "logLevel", "initializeErrorHandling", "ngOnDestroy", "next", "complete", "pipe", "prev", "curr", "message", "statusCode", "Math", "abs", "timestamp", "getTime", "subscribe", "error", "processError", "addError", "context", "enhancedError", "createEnhancedError", "shouldSuppressError", "id", "addCustomError", "category", "SYSTEM", "severity", "MEDIUM", "options", "customError", "isCustomError", "<PERSON><PERSON><PERSON>", "component", "getErrors", "errorFilter", "matchesFilter", "getErrorsLegacy", "getErrorHistory", "asObservable", "getStatistics", "registerUnhandledRequestURL", "url", "push", "isRequestBlackListed", "some", "rq", "includes", "addErrorFilter", "removeErrorFilter", "index", "indexOf", "splice", "clearErrorFilters", "updateConfig", "clearErrorHistory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errorId", "find", "e", "handled", "retryError", "retryFunction", "retryable", "Error", "retryCount", "maxRetries", "totalErrors", "errorsByCategory", "errorsBySeverity", "errorsByStatusCode", "mostCommonErrors", "errorTrends", "Date", "generateErrorId", "isHttpError", "status", "undefined", "determineCategory", "determineSeverity", "categorizeHttpError", "determineSeverityFromStatus", "HIGH", "originalError", "window", "location", "href", "userAgent", "navigator", "isRetryable", "suppressDialog", "addToHistory", "updateStatistics", "shouldSuppressLogging", "logError", "legacyError", "convertToLegacyFormat", "console", "debug", "statusCodes", "error<PERSON><PERSON><PERSON>", "categories", "severities", "urlPatterns", "matchesUrl", "pattern", "suppressLogging", "unshift", "length", "slice", "currentStats", "value", "existingError", "count", "sort", "a", "b", "hourBucket", "setMinutes", "existingTrend", "t", "oneDayAgo", "now", "logMessage", "toUpperCase", "logData", "LOW", "info", "warn", "random", "toString", "substring", "toLowerCase", "AUTHENTICATION", "AUTHORIZATION", "VALIDATION", "BUSINESS_LOGIC", "USER_INPUT", "EXTERNAL_SERVICE", "CRITICAL", "NETWORK", "_error", "errorDetails", "additionalData", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\errors\\error.service.ts"], "sourcesContent": ["import { HttpErrorResponse } from '@angular/common/http';\r\nimport { Injectable, OnDestroy } from '@angular/core';\r\nimport { BehaviorSubject, Observable, Subject } from 'rxjs';\r\nimport {\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  filter as rxFilter,\r\n  takeUntil,\r\n} from 'rxjs/operators';\r\nimport { environment } from '../../../../environments/environment';\r\n\r\n/**\r\n * Error severity levels\r\n */\r\nexport enum ErrorSeverity {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical',\r\n}\r\n\r\n/**\r\n * Error categories for better organization\r\n */\r\nexport enum ErrorCategory {\r\n  NETWORK = 'network',\r\n  AUTHENTICATION = 'authentication',\r\n  AUTHORIZATION = 'authorization',\r\n  VALIDATION = 'validation',\r\n  BUSINESS_LOGIC = 'business_logic',\r\n  SYSTEM = 'system',\r\n  USER_INPUT = 'user_input',\r\n  EXTERNAL_SERVICE = 'external_service',\r\n}\r\n\r\n/**\r\n * Error context interface for additional metadata\r\n */\r\nexport interface ErrorContext {\r\n  userId?: string;\r\n  sessionId?: string;\r\n  userAgent?: string;\r\n  url?: string;\r\n  timestamp?: Date;\r\n  component?: string;\r\n  action?: string;\r\n  additionalData?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Enhanced error interface with comprehensive metadata\r\n */\r\nexport interface EnhancedError {\r\n  id: string;\r\n  originalError: any;\r\n  message: string;\r\n  severity: ErrorSeverity;\r\n  category: ErrorCategory;\r\n  context: ErrorContext;\r\n  isCustomError?: boolean;\r\n  errorKey?: string;\r\n  statusCode?: number;\r\n  retryable?: boolean;\r\n  retryCount?: number;\r\n  maxRetries?: number;\r\n  suppressDialog?: boolean;\r\n  handled?: boolean;\r\n  timestamp: Date;\r\n}\r\n\r\n/**\r\n * Error filter configuration\r\n */\r\nexport interface ErrorFilter {\r\n  urlPatterns?: string[];\r\n  statusCodes?: number[];\r\n  errorKeys?: string[];\r\n  categories?: ErrorCategory[];\r\n  severities?: ErrorSeverity[];\r\n  suppressDialog?: boolean;\r\n  suppressLogging?: boolean;\r\n}\r\n\r\n/**\r\n * Error service configuration\r\n */\r\nexport interface ErrorServiceConfig {\r\n  enableLogging?: boolean;\r\n  enableAnalytics?: boolean;\r\n  maxErrorHistory?: number;\r\n  debounceTime?: number;\r\n  defaultRetryAttempts?: number;\r\n  logLevel?: 'error' | 'warn' | 'info' | 'debug';\r\n}\r\n\r\n/**\r\n * Error statistics for analytics\r\n */\r\nexport interface ErrorStatistics {\r\n  totalErrors: number;\r\n  errorsByCategory: Record<ErrorCategory, number>;\r\n  errorsBySeverity: Record<ErrorSeverity, number>;\r\n  errorsByStatusCode: Record<number, number>;\r\n  mostCommonErrors: Array<{ errorKey: string; count: number }>;\r\n  errorTrends: Array<{ timestamp: Date; count: number }>;\r\n}\r\n\r\n/**\r\n * Legacy error format interface for backward compatibility\r\n */\r\nexport interface LegacyError {\r\n  status?: number;\r\n  statusCode?: number;\r\n  message?: string;\r\n  isCustomError?: boolean;\r\n  suppressDialog?: boolean;\r\n  error?: {\r\n    errorKey?: string;\r\n    message?: string;\r\n    errorDetails?: any;\r\n  };\r\n  // Allow additional properties for flexibility\r\n  [key: string]: any;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ErrorService implements OnDestroy {\r\n  private readonly destroy$ = new Subject<void>();\r\n  private readonly errors$ = new Subject<EnhancedError>();\r\n  private readonly legacyErrors$ = new Subject<LegacyError>(); // For backward compatibility\r\n  private readonly errorHistory$ = new BehaviorSubject<EnhancedError[]>([]);\r\n  private readonly statistics$ = new BehaviorSubject<ErrorStatistics>(\r\n    this.initializeStatistics()\r\n  );\r\n\r\n  private requestBlackList: string[] = [];\r\n  private errorFilters: ErrorFilter[] = [];\r\n  private errorHistory: EnhancedError[] = [];\r\n  private config: ErrorServiceConfig;\r\n\r\n  constructor() {\r\n    this.config = {\r\n      enableLogging: !environment.production,\r\n      enableAnalytics: true,\r\n      maxErrorHistory: 100,\r\n      debounceTime: 300,\r\n      defaultRetryAttempts: 3,\r\n      logLevel: environment.production ? 'error' : 'debug',\r\n    };\r\n\r\n    this.initializeErrorHandling();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.errors$.complete();\r\n    this.legacyErrors$.complete();\r\n    this.errorHistory$.complete();\r\n    this.statistics$.complete();\r\n  }\r\n\r\n  /**\r\n   * Initialize error handling pipeline\r\n   */\r\n  private initializeErrorHandling(): void {\r\n    this.errors$\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        debounceTime(this.config.debounceTime || 300),\r\n        distinctUntilChanged(\r\n          (prev, curr) =>\r\n            prev.message === curr.message &&\r\n            prev.statusCode === curr.statusCode &&\r\n            Math.abs(curr.timestamp.getTime() - prev.timestamp.getTime()) < 5000\r\n        )\r\n      )\r\n      .subscribe((error) => {\r\n        this.processError(error);\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Add error with enhanced metadata and processing\r\n   */\r\n  public addError(\r\n    error: LegacyError | HttpErrorResponse | Error,\r\n    context?: Partial<ErrorContext>\r\n  ): string {\r\n    const enhancedError = this.createEnhancedError(error, context);\r\n\r\n    if (this.shouldSuppressError(enhancedError)) {\r\n      return enhancedError.id;\r\n    }\r\n\r\n    this.errors$.next(enhancedError);\r\n    return enhancedError.id;\r\n  }\r\n\r\n  /**\r\n   * Add custom error with specific configuration\r\n   */\r\n  public addCustomError(\r\n    message: string,\r\n    category: ErrorCategory = ErrorCategory.SYSTEM,\r\n    severity: ErrorSeverity = ErrorSeverity.MEDIUM,\r\n    context?: Partial<ErrorContext>,\r\n    options?: {\r\n      errorKey?: string;\r\n      retryable?: boolean;\r\n      suppressDialog?: boolean;\r\n    }\r\n  ): string {\r\n    const customError = {\r\n      isCustomError: true,\r\n      message,\r\n      category,\r\n      severity,\r\n      error: {\r\n        errorKey: options?.errorKey,\r\n        message,\r\n      },\r\n      ...options,\r\n    };\r\n\r\n    return this.addError(customError, {\r\n      ...context,\r\n      component: context?.component || 'custom',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get errors observable with optional filtering\r\n   */\r\n  public getErrors(\r\n    errorFilter?: Partial<ErrorFilter>\r\n  ): Observable<EnhancedError> {\r\n    return this.errors$.pipe(\r\n      rxFilter((error: EnhancedError) =>\r\n        this.matchesFilter(error, errorFilter)\r\n      ),\r\n      takeUntil(this.destroy$)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get errors observable (backward compatibility - returns legacy format)\r\n   * @deprecated Use getErrors() instead for enhanced error handling\r\n   */\r\n  public getErrorsLegacy(): Observable<any> {\r\n    return this.legacyErrors$.pipe(takeUntil(this.destroy$));\r\n  }\r\n\r\n  /**\r\n   * Get error history\r\n   */\r\n  public getErrorHistory(): Observable<EnhancedError[]> {\r\n    return this.errorHistory$.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Get error statistics\r\n   */\r\n  public getStatistics(): Observable<ErrorStatistics> {\r\n    return this.statistics$.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Register unhandled URL to the blacklist (backward compatibility)\r\n   */\r\n  public registerUnhandledRequestURL(url: string): void {\r\n    this.requestBlackList.push(url);\r\n  }\r\n\r\n  /**\r\n   * Check if request URL is blacklisted (backward compatibility)\r\n   */\r\n  public isRequestBlackListed(url: string): boolean {\r\n    return this.requestBlackList.some((rq) => url.includes(rq));\r\n  }\r\n\r\n  /**\r\n   * Add error filter\r\n   */\r\n  public addErrorFilter(filter: ErrorFilter): void {\r\n    this.errorFilters.push(filter);\r\n  }\r\n\r\n  /**\r\n   * Remove error filter\r\n   */\r\n  public removeErrorFilter(filter: ErrorFilter): void {\r\n    const index = this.errorFilters.indexOf(filter);\r\n    if (index > -1) {\r\n      this.errorFilters.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear all error filters\r\n   */\r\n  public clearErrorFilters(): void {\r\n    this.errorFilters = [];\r\n  }\r\n\r\n  /**\r\n   * Update service configuration\r\n   */\r\n  public updateConfig(config: Partial<ErrorServiceConfig>): void {\r\n    this.config = { ...this.config, ...config };\r\n  }\r\n\r\n  /**\r\n   * Clear error history\r\n   */\r\n  public clearErrorHistory(): void {\r\n    this.errorHistory = [];\r\n    this.errorHistory$.next([]);\r\n  }\r\n\r\n  /**\r\n   * Mark error as handled\r\n   */\r\n  public markErrorAsHandled(errorId: string): void {\r\n    const error = this.errorHistory.find((e) => e.id === errorId);\r\n    if (error) {\r\n      error.handled = true;\r\n      this.errorHistory$.next([...this.errorHistory]);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retry failed operation\r\n   */\r\n  public retryError(\r\n    errorId: string,\r\n    retryFunction: () => Observable<any>\r\n  ): Observable<any> {\r\n    const error = this.errorHistory.find((e) => e.id === errorId);\r\n    if (!error || !error.retryable) {\r\n      throw new Error('Error is not retryable or not found');\r\n    }\r\n\r\n    error.retryCount = (error.retryCount || 0) + 1;\r\n\r\n    if (\r\n      error.retryCount >\r\n      (error.maxRetries || this.config.defaultRetryAttempts || 3)\r\n    ) {\r\n      throw new Error('Maximum retry attempts exceeded');\r\n    }\r\n\r\n    return retryFunction();\r\n  }\r\n\r\n  /**\r\n   * Initialize statistics object\r\n   */\r\n  private initializeStatistics(): ErrorStatistics {\r\n    return {\r\n      totalErrors: 0,\r\n      errorsByCategory: {} as Record<ErrorCategory, number>,\r\n      errorsBySeverity: {} as Record<ErrorSeverity, number>,\r\n      errorsByStatusCode: {},\r\n      mostCommonErrors: [],\r\n      errorTrends: [],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create enhanced error from raw error\r\n   */\r\n  private createEnhancedError(\r\n    error: any,\r\n    context?: Partial<ErrorContext>\r\n  ): EnhancedError {\r\n    const timestamp = new Date();\r\n    const id = this.generateErrorId();\r\n\r\n    // Extract error information\r\n    const isHttpError = error?.status !== undefined;\r\n    const isCustomError = error?.isCustomError === true;\r\n\r\n    let message = '';\r\n    let statusCode: number | undefined;\r\n    let errorKey: string | undefined;\r\n    let category = ErrorCategory.SYSTEM;\r\n    let severity = ErrorSeverity.MEDIUM;\r\n\r\n    if (isCustomError) {\r\n      message = error.message || 'Custom error occurred';\r\n      errorKey = error.error?.errorKey;\r\n      category = this.determineCategory(error);\r\n      severity = this.determineSeverity(error);\r\n    } else if (isHttpError) {\r\n      statusCode = error.status;\r\n      message =\r\n        error.error?.message || error.message || `HTTP ${statusCode} Error`;\r\n      errorKey = error.error?.errorKey;\r\n      category = this.categorizeHttpError(statusCode || 0);\r\n      severity = this.determineSeverityFromStatus(statusCode || 0);\r\n    } else {\r\n      message = error?.message || 'Unknown error occurred';\r\n      category = ErrorCategory.SYSTEM;\r\n      severity = ErrorSeverity.HIGH;\r\n    }\r\n\r\n    const enhancedError: EnhancedError = {\r\n      id,\r\n      originalError: error,\r\n      message,\r\n      severity,\r\n      category,\r\n      context: {\r\n        timestamp,\r\n        url: window.location.href,\r\n        userAgent: navigator.userAgent,\r\n        ...context,\r\n      },\r\n      isCustomError,\r\n      errorKey,\r\n      statusCode,\r\n      retryable: this.isRetryable(error, statusCode),\r\n      retryCount: 0,\r\n      maxRetries: this.config.defaultRetryAttempts,\r\n      suppressDialog: error?.suppressDialog || false,\r\n      handled: false,\r\n      timestamp,\r\n    };\r\n\r\n    return enhancedError;\r\n  }\r\n\r\n  /**\r\n   * Process error through the pipeline\r\n   */\r\n  private processError(error: EnhancedError): void {\r\n    // Add to history\r\n    this.addToHistory(error);\r\n\r\n    // Update statistics\r\n    this.updateStatistics(error);\r\n\r\n    // Log error if enabled\r\n    if (this.config.enableLogging && !this.shouldSuppressLogging(error)) {\r\n      this.logError(error);\r\n    }\r\n\r\n    // Emit for dialog display (backward compatibility)\r\n    if (!error.suppressDialog) {\r\n      // Convert back to original format for backward compatibility\r\n      const legacyError = this.convertToLegacyFormat(error);\r\n      this.legacyErrors$.next(legacyError);\r\n      console.debug('Legacy error format for dialog:', legacyError);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if error should be suppressed\r\n   */\r\n  private shouldSuppressError(error: EnhancedError): boolean {\r\n    // Check URL blacklist\r\n    if (error.context.url && this.isRequestBlackListed(error.context.url)) {\r\n      return true;\r\n    }\r\n\r\n    // Check error filters\r\n    return this.errorFilters.some((filter) =>\r\n      this.matchesFilter(error, filter)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if error matches filter\r\n   */\r\n  private matchesFilter(\r\n    error: EnhancedError,\r\n    filter?: Partial<ErrorFilter>\r\n  ): boolean {\r\n    if (!filter) return true;\r\n\r\n    if (\r\n      filter.statusCodes &&\r\n      error.statusCode &&\r\n      !filter.statusCodes.includes(error.statusCode)\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    if (\r\n      filter.errorKeys &&\r\n      error.errorKey &&\r\n      !filter.errorKeys.includes(error.errorKey)\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    if (filter.categories && !filter.categories.includes(error.category)) {\r\n      return false;\r\n    }\r\n\r\n    if (filter.severities && !filter.severities.includes(error.severity)) {\r\n      return false;\r\n    }\r\n\r\n    if (filter.urlPatterns && error.context.url) {\r\n      const matchesUrl = filter.urlPatterns.some((pattern) =>\r\n        error.context.url!.includes(pattern)\r\n      );\r\n      if (!matchesUrl) return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Check if logging should be suppressed for this error\r\n   */\r\n  private shouldSuppressLogging(error: EnhancedError): boolean {\r\n    return this.errorFilters.some(\r\n      (filter) => filter.suppressLogging && this.matchesFilter(error, filter)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Add error to history with size management\r\n   */\r\n  private addToHistory(error: EnhancedError): void {\r\n    this.errorHistory.unshift(error);\r\n\r\n    // Maintain max history size\r\n    if (this.errorHistory.length > (this.config.maxErrorHistory || 100)) {\r\n      this.errorHistory = this.errorHistory.slice(\r\n        0,\r\n        this.config.maxErrorHistory || 100\r\n      );\r\n    }\r\n\r\n    this.errorHistory$.next([...this.errorHistory]);\r\n  }\r\n\r\n  /**\r\n   * Update error statistics\r\n   */\r\n  private updateStatistics(error: EnhancedError): void {\r\n    const currentStats = this.statistics$.value;\r\n\r\n    // Update total count\r\n    currentStats.totalErrors++;\r\n\r\n    // Update category count\r\n    currentStats.errorsByCategory[error.category] =\r\n      (currentStats.errorsByCategory[error.category] || 0) + 1;\r\n\r\n    // Update severity count\r\n    currentStats.errorsBySeverity[error.severity] =\r\n      (currentStats.errorsBySeverity[error.severity] || 0) + 1;\r\n\r\n    // Update status code count\r\n    if (error.statusCode) {\r\n      currentStats.errorsByStatusCode[error.statusCode] =\r\n        (currentStats.errorsByStatusCode[error.statusCode] || 0) + 1;\r\n    }\r\n\r\n    // Update most common errors\r\n    if (error.errorKey) {\r\n      const existingError = currentStats.mostCommonErrors.find(\r\n        (e) => e.errorKey === error.errorKey\r\n      );\r\n      if (existingError) {\r\n        existingError.count++;\r\n      } else {\r\n        currentStats.mostCommonErrors.push({\r\n          errorKey: error.errorKey,\r\n          count: 1,\r\n        });\r\n      }\r\n\r\n      // Sort and keep top 10\r\n      currentStats.mostCommonErrors.sort((a, b) => b.count - a.count);\r\n      currentStats.mostCommonErrors = currentStats.mostCommonErrors.slice(\r\n        0,\r\n        10\r\n      );\r\n    }\r\n\r\n    // Update trends (hourly buckets)\r\n    const hourBucket = new Date(error.timestamp);\r\n    hourBucket.setMinutes(0, 0, 0);\r\n\r\n    const existingTrend = currentStats.errorTrends.find(\r\n      (t) => t.timestamp.getTime() === hourBucket.getTime()\r\n    );\r\n\r\n    if (existingTrend) {\r\n      existingTrend.count++;\r\n    } else {\r\n      currentStats.errorTrends.push({ timestamp: hourBucket, count: 1 });\r\n    }\r\n\r\n    // Keep only last 24 hours\r\n    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\r\n    currentStats.errorTrends = currentStats.errorTrends.filter(\r\n      (t) => t.timestamp >= oneDayAgo\r\n    );\r\n\r\n    this.statistics$.next(currentStats);\r\n  }\r\n\r\n  /**\r\n   * Log error based on configuration\r\n   */\r\n  private logError(error: EnhancedError): void {\r\n    const logLevel = this.config.logLevel || 'error';\r\n    const logMessage = `[${error.severity.toUpperCase()}] ${error.category}: ${\r\n      error.message\r\n    }`;\r\n    const logData = {\r\n      id: error.id,\r\n      category: error.category,\r\n      severity: error.severity,\r\n      statusCode: error.statusCode,\r\n      errorKey: error.errorKey,\r\n      context: error.context,\r\n      originalError: error.originalError,\r\n    };\r\n\r\n    switch (logLevel) {\r\n      case 'debug':\r\n        console.debug(logMessage, logData);\r\n        break;\r\n      case 'info':\r\n        if (error.severity === ErrorSeverity.LOW) {\r\n          console.info(logMessage, logData);\r\n        } else {\r\n          console.error(logMessage, logData);\r\n        }\r\n        break;\r\n      case 'warn':\r\n        if (\r\n          error.severity === ErrorSeverity.LOW ||\r\n          error.severity === ErrorSeverity.MEDIUM\r\n        ) {\r\n          console.warn(logMessage, logData);\r\n        } else {\r\n          console.error(logMessage, logData);\r\n        }\r\n        break;\r\n      case 'error':\r\n      default:\r\n        console.error(logMessage, logData);\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate unique error ID\r\n   */\r\n  private generateErrorId(): string {\r\n    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\r\n  }\r\n\r\n  /**\r\n   * Determine error category from custom error\r\n   */\r\n  private determineCategory(error: any): ErrorCategory {\r\n    const errorKey = error.error?.errorKey?.toLowerCase() || '';\r\n\r\n    if (\r\n      errorKey.includes('auth') ||\r\n      errorKey.includes('login') ||\r\n      errorKey.includes('token')\r\n    ) {\r\n      return ErrorCategory.AUTHENTICATION;\r\n    }\r\n    if (\r\n      errorKey.includes('permission') ||\r\n      errorKey.includes('access') ||\r\n      errorKey.includes('forbidden')\r\n    ) {\r\n      return ErrorCategory.AUTHORIZATION;\r\n    }\r\n    if (\r\n      errorKey.includes('validation') ||\r\n      errorKey.includes('invalid') ||\r\n      errorKey.includes('required')\r\n    ) {\r\n      return ErrorCategory.VALIDATION;\r\n    }\r\n    if (\r\n      errorKey.includes('business') ||\r\n      errorKey.includes('rule') ||\r\n      errorKey.includes('logic')\r\n    ) {\r\n      return ErrorCategory.BUSINESS_LOGIC;\r\n    }\r\n    if (errorKey.includes('input') || errorKey.includes('user')) {\r\n      return ErrorCategory.USER_INPUT;\r\n    }\r\n    if (\r\n      errorKey.includes('external') ||\r\n      errorKey.includes('service') ||\r\n      errorKey.includes('api')\r\n    ) {\r\n      return ErrorCategory.EXTERNAL_SERVICE;\r\n    }\r\n\r\n    return ErrorCategory.SYSTEM;\r\n  }\r\n\r\n  /**\r\n   * Determine error severity from custom error\r\n   */\r\n  private determineSeverity(error: any): ErrorSeverity {\r\n    const errorKey = error.error?.errorKey?.toLowerCase() || '';\r\n\r\n    if (errorKey.includes('critical') || errorKey.includes('fatal')) {\r\n      return ErrorSeverity.CRITICAL;\r\n    }\r\n    if (errorKey.includes('high') || errorKey.includes('severe')) {\r\n      return ErrorSeverity.HIGH;\r\n    }\r\n    if (errorKey.includes('low') || errorKey.includes('minor')) {\r\n      return ErrorSeverity.LOW;\r\n    }\r\n\r\n    return ErrorSeverity.MEDIUM;\r\n  }\r\n\r\n  /**\r\n   * Categorize HTTP error by status code\r\n   */\r\n  private categorizeHttpError(statusCode: number): ErrorCategory {\r\n    if (statusCode === 401) {\r\n      return ErrorCategory.AUTHENTICATION;\r\n    }\r\n    if (statusCode === 403) {\r\n      return ErrorCategory.AUTHORIZATION;\r\n    }\r\n    if (statusCode >= 400 && statusCode < 500) {\r\n      return ErrorCategory.VALIDATION;\r\n    }\r\n    if (statusCode >= 500) {\r\n      return ErrorCategory.SYSTEM;\r\n    }\r\n    if (statusCode === 0 || statusCode < 0) {\r\n      return ErrorCategory.NETWORK;\r\n    }\r\n\r\n    return ErrorCategory.SYSTEM;\r\n  }\r\n\r\n  /**\r\n   * Determine severity from HTTP status code\r\n   */\r\n  private determineSeverityFromStatus(statusCode: number): ErrorSeverity {\r\n    if (statusCode >= 500) {\r\n      return ErrorSeverity.HIGH;\r\n    }\r\n    if (statusCode === 401 || statusCode === 403) {\r\n      return ErrorSeverity.MEDIUM;\r\n    }\r\n    if (statusCode === 404) {\r\n      return ErrorSeverity.LOW;\r\n    }\r\n    if (statusCode >= 400 && statusCode < 500) {\r\n      return ErrorSeverity.MEDIUM;\r\n    }\r\n    if (statusCode === 0 || statusCode < 0) {\r\n      return ErrorSeverity.HIGH;\r\n    }\r\n\r\n    return ErrorSeverity.MEDIUM;\r\n  }\r\n\r\n  /**\r\n   * Check if error is retryable\r\n   */\r\n  private isRetryable(_error: any, statusCode?: number): boolean {\r\n    // Network errors are usually retryable\r\n    if (statusCode === 0 || statusCode === undefined) {\r\n      return true;\r\n    }\r\n\r\n    // Server errors are retryable\r\n    if (statusCode >= 500) {\r\n      return true;\r\n    }\r\n\r\n    // Timeout errors are retryable\r\n    if (statusCode === 408 || statusCode === 504) {\r\n      return true;\r\n    }\r\n\r\n    // Rate limiting is retryable\r\n    if (statusCode === 429) {\r\n      return true;\r\n    }\r\n\r\n    // Client errors are generally not retryable\r\n    if (statusCode >= 400 && statusCode < 500) {\r\n      return false;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Convert enhanced error back to legacy format for backward compatibility\r\n   */\r\n  private convertToLegacyFormat(error: EnhancedError): any {\r\n    return {\r\n      ...error.originalError,\r\n      status: error.statusCode,\r\n      error: {\r\n        errorKey: error.errorKey,\r\n        message: error.message,\r\n        errorDetails: error.context.additionalData,\r\n      },\r\n      isCustomError: error.isCustomError,\r\n    };\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,OAAO,QAAQ,MAAM;AAC3D,SACEC,YAAY,EACZC,oBAAoB,EACpBC,MAAM,IAAIC,QAAQ,EAClBC,SAAS,QACJ,gBAAgB;AACvB,SAASC,WAAW,QAAQ,sCAAsC;;AAElE;;;AAGA,WAAYC,aAKX;AALD,WAAYA,aAAa;EACvBA,aAAA,eAAW;EACXA,aAAA,qBAAiB;EACjBA,aAAA,iBAAa;EACbA,aAAA,yBAAqB;AACvB,CAAC,EALWA,aAAa,KAAbA,aAAa;AAOzB;;;AAGA,WAAYC,aASX;AATD,WAAYA,aAAa;EACvBA,aAAA,uBAAmB;EACnBA,aAAA,qCAAiC;EACjCA,aAAA,mCAA+B;EAC/BA,aAAA,6BAAyB;EACzBA,aAAA,qCAAiC;EACjCA,aAAA,qBAAiB;EACjBA,aAAA,6BAAyB;EACzBA,aAAA,yCAAqC;AACvC,CAAC,EATWA,aAAa,KAAbA,aAAa;AAwGzB,OAAM,MAAOC,YAAY;EAcvBC,YAAA;IAbiB,KAAAC,QAAQ,GAAG,IAAIX,OAAO,EAAQ;IAC9B,KAAAY,OAAO,GAAG,IAAIZ,OAAO,EAAiB;IACtC,KAAAa,aAAa,GAAG,IAAIb,OAAO,EAAe,CAAC,CAAC;IAC5C,KAAAc,aAAa,GAAG,IAAIf,eAAe,CAAkB,EAAE,CAAC;IACxD,KAAAgB,WAAW,GAAG,IAAIhB,eAAe,CAChD,IAAI,CAACiB,oBAAoB,EAAE,CAC5B;IAEO,KAAAC,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,YAAY,GAAoB,EAAE;IAIxC,IAAI,CAACC,MAAM,GAAG;MACZC,aAAa,EAAE,CAACf,WAAW,CAACgB,UAAU;MACtCC,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE,GAAG;MACpBvB,YAAY,EAAE,GAAG;MACjBwB,oBAAoB,EAAE,CAAC;MACvBC,QAAQ,EAAEpB,WAAW,CAACgB,UAAU,GAAG,OAAO,GAAG;KAC9C;IAED,IAAI,CAACK,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjB,QAAQ,CAACkB,IAAI,EAAE;IACpB,IAAI,CAAClB,QAAQ,CAACmB,QAAQ,EAAE;IACxB,IAAI,CAAClB,OAAO,CAACkB,QAAQ,EAAE;IACvB,IAAI,CAACjB,aAAa,CAACiB,QAAQ,EAAE;IAC7B,IAAI,CAAChB,aAAa,CAACgB,QAAQ,EAAE;IAC7B,IAAI,CAACf,WAAW,CAACe,QAAQ,EAAE;EAC7B;EAEA;;;EAGQH,uBAAuBA,CAAA;IAC7B,IAAI,CAACf,OAAO,CACTmB,IAAI,CACH1B,SAAS,CAAC,IAAI,CAACM,QAAQ,CAAC,EACxBV,YAAY,CAAC,IAAI,CAACmB,MAAM,CAACnB,YAAY,IAAI,GAAG,CAAC,EAC7CC,oBAAoB,CAClB,CAAC8B,IAAI,EAAEC,IAAI,KACTD,IAAI,CAACE,OAAO,KAAKD,IAAI,CAACC,OAAO,IAC7BF,IAAI,CAACG,UAAU,KAAKF,IAAI,CAACE,UAAU,IACnCC,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACK,SAAS,CAACC,OAAO,EAAE,GAAGP,IAAI,CAACM,SAAS,CAACC,OAAO,EAAE,CAAC,GAAG,IAAI,CACvE,CACF,CACAC,SAAS,CAAEC,KAAK,IAAI;MACnB,IAAI,CAACC,YAAY,CAACD,KAAK,CAAC;IAC1B,CAAC,CAAC;EACN;EAEA;;;EAGOE,QAAQA,CACbF,KAA8C,EAC9CG,OAA+B;IAE/B,MAAMC,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAACL,KAAK,EAAEG,OAAO,CAAC;IAE9D,IAAI,IAAI,CAACG,mBAAmB,CAACF,aAAa,CAAC,EAAE;MAC3C,OAAOA,aAAa,CAACG,EAAE;;IAGzB,IAAI,CAACpC,OAAO,CAACiB,IAAI,CAACgB,aAAa,CAAC;IAChC,OAAOA,aAAa,CAACG,EAAE;EACzB;EAEA;;;EAGOC,cAAcA,CACnBf,OAAe,EACfgB,QAAA,GAA0B1C,aAAa,CAAC2C,MAAM,EAC9CC,QAAA,GAA0B7C,aAAa,CAAC8C,MAAM,EAC9CT,OAA+B,EAC/BU,OAIC;IAED,MAAMC,WAAW,GAAG;MAClBC,aAAa,EAAE,IAAI;MACnBtB,OAAO;MACPgB,QAAQ;MACRE,QAAQ;MACRX,KAAK,EAAE;QACLgB,QAAQ,EAAEH,OAAO,EAAEG,QAAQ;QAC3BvB;OACD;MACD,GAAGoB;KACJ;IAED,OAAO,IAAI,CAACX,QAAQ,CAACY,WAAW,EAAE;MAChC,GAAGX,OAAO;MACVc,SAAS,EAAEd,OAAO,EAAEc,SAAS,IAAI;KAClC,CAAC;EACJ;EAEA;;;EAGOC,SAASA,CACdC,WAAkC;IAElC,OAAO,IAAI,CAAChD,OAAO,CAACmB,IAAI,CACtB3B,QAAQ,CAAEqC,KAAoB,IAC5B,IAAI,CAACoB,aAAa,CAACpB,KAAK,EAAEmB,WAAW,CAAC,CACvC,EACDvD,SAAS,CAAC,IAAI,CAACM,QAAQ,CAAC,CACzB;EACH;EAEA;;;;EAIOmD,eAAeA,CAAA;IACpB,OAAO,IAAI,CAACjD,aAAa,CAACkB,IAAI,CAAC1B,SAAS,CAAC,IAAI,CAACM,QAAQ,CAAC,CAAC;EAC1D;EAEA;;;EAGOoD,eAAeA,CAAA;IACpB,OAAO,IAAI,CAACjD,aAAa,CAACkD,YAAY,EAAE;EAC1C;EAEA;;;EAGOC,aAAaA,CAAA;IAClB,OAAO,IAAI,CAAClD,WAAW,CAACiD,YAAY,EAAE;EACxC;EAEA;;;EAGOE,2BAA2BA,CAACC,GAAW;IAC5C,IAAI,CAAClD,gBAAgB,CAACmD,IAAI,CAACD,GAAG,CAAC;EACjC;EAEA;;;EAGOE,oBAAoBA,CAACF,GAAW;IACrC,OAAO,IAAI,CAAClD,gBAAgB,CAACqD,IAAI,CAAEC,EAAE,IAAKJ,GAAG,CAACK,QAAQ,CAACD,EAAE,CAAC,CAAC;EAC7D;EAEA;;;EAGOE,cAAcA,CAACtE,MAAmB;IACvC,IAAI,CAACe,YAAY,CAACkD,IAAI,CAACjE,MAAM,CAAC;EAChC;EAEA;;;EAGOuE,iBAAiBA,CAACvE,MAAmB;IAC1C,MAAMwE,KAAK,GAAG,IAAI,CAACzD,YAAY,CAAC0D,OAAO,CAACzE,MAAM,CAAC;IAC/C,IAAIwE,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACzD,YAAY,CAAC2D,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;;EAEtC;EAEA;;;EAGOG,iBAAiBA,CAAA;IACtB,IAAI,CAAC5D,YAAY,GAAG,EAAE;EACxB;EAEA;;;EAGO6D,YAAYA,CAAC3D,MAAmC;IACrD,IAAI,CAACA,MAAM,GAAG;MAAE,GAAG,IAAI,CAACA,MAAM;MAAE,GAAGA;IAAM,CAAE;EAC7C;EAEA;;;EAGO4D,iBAAiBA,CAAA;IACtB,IAAI,CAAC7D,YAAY,GAAG,EAAE;IACtB,IAAI,CAACL,aAAa,CAACe,IAAI,CAAC,EAAE,CAAC;EAC7B;EAEA;;;EAGOoD,kBAAkBA,CAACC,OAAe;IACvC,MAAMzC,KAAK,GAAG,IAAI,CAACtB,YAAY,CAACgE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACpC,EAAE,KAAKkC,OAAO,CAAC;IAC7D,IAAIzC,KAAK,EAAE;MACTA,KAAK,CAAC4C,OAAO,GAAG,IAAI;MACpB,IAAI,CAACvE,aAAa,CAACe,IAAI,CAAC,CAAC,GAAG,IAAI,CAACV,YAAY,CAAC,CAAC;;EAEnD;EAEA;;;EAGOmE,UAAUA,CACfJ,OAAe,EACfK,aAAoC;IAEpC,MAAM9C,KAAK,GAAG,IAAI,CAACtB,YAAY,CAACgE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACpC,EAAE,KAAKkC,OAAO,CAAC;IAC7D,IAAI,CAACzC,KAAK,IAAI,CAACA,KAAK,CAAC+C,SAAS,EAAE;MAC9B,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;;IAGxDhD,KAAK,CAACiD,UAAU,GAAG,CAACjD,KAAK,CAACiD,UAAU,IAAI,CAAC,IAAI,CAAC;IAE9C,IACEjD,KAAK,CAACiD,UAAU,IACfjD,KAAK,CAACkD,UAAU,IAAI,IAAI,CAACvE,MAAM,CAACK,oBAAoB,IAAI,CAAC,CAAC,EAC3D;MACA,MAAM,IAAIgE,KAAK,CAAC,iCAAiC,CAAC;;IAGpD,OAAOF,aAAa,EAAE;EACxB;EAEA;;;EAGQvE,oBAAoBA,CAAA;IAC1B,OAAO;MACL4E,WAAW,EAAE,CAAC;MACdC,gBAAgB,EAAE,EAAmC;MACrDC,gBAAgB,EAAE,EAAmC;MACrDC,kBAAkB,EAAE,EAAE;MACtBC,gBAAgB,EAAE,EAAE;MACpBC,WAAW,EAAE;KACd;EACH;EAEA;;;EAGQnD,mBAAmBA,CACzBL,KAAU,EACVG,OAA+B;IAE/B,MAAMN,SAAS,GAAG,IAAI4D,IAAI,EAAE;IAC5B,MAAMlD,EAAE,GAAG,IAAI,CAACmD,eAAe,EAAE;IAEjC;IACA,MAAMC,WAAW,GAAG3D,KAAK,EAAE4D,MAAM,KAAKC,SAAS;IAC/C,MAAM9C,aAAa,GAAGf,KAAK,EAAEe,aAAa,KAAK,IAAI;IAEnD,IAAItB,OAAO,GAAG,EAAE;IAChB,IAAIC,UAA8B;IAClC,IAAIsB,QAA4B;IAChC,IAAIP,QAAQ,GAAG1C,aAAa,CAAC2C,MAAM;IACnC,IAAIC,QAAQ,GAAG7C,aAAa,CAAC8C,MAAM;IAEnC,IAAIG,aAAa,EAAE;MACjBtB,OAAO,GAAGO,KAAK,CAACP,OAAO,IAAI,uBAAuB;MAClDuB,QAAQ,GAAGhB,KAAK,CAACA,KAAK,EAAEgB,QAAQ;MAChCP,QAAQ,GAAG,IAAI,CAACqD,iBAAiB,CAAC9D,KAAK,CAAC;MACxCW,QAAQ,GAAG,IAAI,CAACoD,iBAAiB,CAAC/D,KAAK,CAAC;KACzC,MAAM,IAAI2D,WAAW,EAAE;MACtBjE,UAAU,GAAGM,KAAK,CAAC4D,MAAM;MACzBnE,OAAO,GACLO,KAAK,CAACA,KAAK,EAAEP,OAAO,IAAIO,KAAK,CAACP,OAAO,IAAI,QAAQC,UAAU,QAAQ;MACrEsB,QAAQ,GAAGhB,KAAK,CAACA,KAAK,EAAEgB,QAAQ;MAChCP,QAAQ,GAAG,IAAI,CAACuD,mBAAmB,CAACtE,UAAU,IAAI,CAAC,CAAC;MACpDiB,QAAQ,GAAG,IAAI,CAACsD,2BAA2B,CAACvE,UAAU,IAAI,CAAC,CAAC;KAC7D,MAAM;MACLD,OAAO,GAAGO,KAAK,EAAEP,OAAO,IAAI,wBAAwB;MACpDgB,QAAQ,GAAG1C,aAAa,CAAC2C,MAAM;MAC/BC,QAAQ,GAAG7C,aAAa,CAACoG,IAAI;;IAG/B,MAAM9D,aAAa,GAAkB;MACnCG,EAAE;MACF4D,aAAa,EAAEnE,KAAK;MACpBP,OAAO;MACPkB,QAAQ;MACRF,QAAQ;MACRN,OAAO,EAAE;QACPN,SAAS;QACT6B,GAAG,EAAE0C,MAAM,CAACC,QAAQ,CAACC,IAAI;QACzBC,SAAS,EAAEC,SAAS,CAACD,SAAS;QAC9B,GAAGpE;OACJ;MACDY,aAAa;MACbC,QAAQ;MACRtB,UAAU;MACVqD,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAACzE,KAAK,EAAEN,UAAU,CAAC;MAC9CuD,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,IAAI,CAACvE,MAAM,CAACK,oBAAoB;MAC5C0F,cAAc,EAAE1E,KAAK,EAAE0E,cAAc,IAAI,KAAK;MAC9C9B,OAAO,EAAE,KAAK;MACd/C;KACD;IAED,OAAOO,aAAa;EACtB;EAEA;;;EAGQH,YAAYA,CAACD,KAAoB;IACvC;IACA,IAAI,CAAC2E,YAAY,CAAC3E,KAAK,CAAC;IAExB;IACA,IAAI,CAAC4E,gBAAgB,CAAC5E,KAAK,CAAC;IAE5B;IACA,IAAI,IAAI,CAACrB,MAAM,CAACC,aAAa,IAAI,CAAC,IAAI,CAACiG,qBAAqB,CAAC7E,KAAK,CAAC,EAAE;MACnE,IAAI,CAAC8E,QAAQ,CAAC9E,KAAK,CAAC;;IAGtB;IACA,IAAI,CAACA,KAAK,CAAC0E,cAAc,EAAE;MACzB;MACA,MAAMK,WAAW,GAAG,IAAI,CAACC,qBAAqB,CAAChF,KAAK,CAAC;MACrD,IAAI,CAAC5B,aAAa,CAACgB,IAAI,CAAC2F,WAAW,CAAC;MACpCE,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEH,WAAW,CAAC;;EAEjE;EAEA;;;EAGQzE,mBAAmBA,CAACN,KAAoB;IAC9C;IACA,IAAIA,KAAK,CAACG,OAAO,CAACuB,GAAG,IAAI,IAAI,CAACE,oBAAoB,CAAC5B,KAAK,CAACG,OAAO,CAACuB,GAAG,CAAC,EAAE;MACrE,OAAO,IAAI;;IAGb;IACA,OAAO,IAAI,CAACjD,YAAY,CAACoD,IAAI,CAAEnE,MAAM,IACnC,IAAI,CAAC0D,aAAa,CAACpB,KAAK,EAAEtC,MAAM,CAAC,CAClC;EACH;EAEA;;;EAGQ0D,aAAaA,CACnBpB,KAAoB,EACpBtC,MAA6B;IAE7B,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;IAExB,IACEA,MAAM,CAACyH,WAAW,IAClBnF,KAAK,CAACN,UAAU,IAChB,CAAChC,MAAM,CAACyH,WAAW,CAACpD,QAAQ,CAAC/B,KAAK,CAACN,UAAU,CAAC,EAC9C;MACA,OAAO,KAAK;;IAGd,IACEhC,MAAM,CAAC0H,SAAS,IAChBpF,KAAK,CAACgB,QAAQ,IACd,CAACtD,MAAM,CAAC0H,SAAS,CAACrD,QAAQ,CAAC/B,KAAK,CAACgB,QAAQ,CAAC,EAC1C;MACA,OAAO,KAAK;;IAGd,IAAItD,MAAM,CAAC2H,UAAU,IAAI,CAAC3H,MAAM,CAAC2H,UAAU,CAACtD,QAAQ,CAAC/B,KAAK,CAACS,QAAQ,CAAC,EAAE;MACpE,OAAO,KAAK;;IAGd,IAAI/C,MAAM,CAAC4H,UAAU,IAAI,CAAC5H,MAAM,CAAC4H,UAAU,CAACvD,QAAQ,CAAC/B,KAAK,CAACW,QAAQ,CAAC,EAAE;MACpE,OAAO,KAAK;;IAGd,IAAIjD,MAAM,CAAC6H,WAAW,IAAIvF,KAAK,CAACG,OAAO,CAACuB,GAAG,EAAE;MAC3C,MAAM8D,UAAU,GAAG9H,MAAM,CAAC6H,WAAW,CAAC1D,IAAI,CAAE4D,OAAO,IACjDzF,KAAK,CAACG,OAAO,CAACuB,GAAI,CAACK,QAAQ,CAAC0D,OAAO,CAAC,CACrC;MACD,IAAI,CAACD,UAAU,EAAE,OAAO,KAAK;;IAG/B,OAAO,IAAI;EACb;EAEA;;;EAGQX,qBAAqBA,CAAC7E,KAAoB;IAChD,OAAO,IAAI,CAACvB,YAAY,CAACoD,IAAI,CAC1BnE,MAAM,IAAKA,MAAM,CAACgI,eAAe,IAAI,IAAI,CAACtE,aAAa,CAACpB,KAAK,EAAEtC,MAAM,CAAC,CACxE;EACH;EAEA;;;EAGQiH,YAAYA,CAAC3E,KAAoB;IACvC,IAAI,CAACtB,YAAY,CAACiH,OAAO,CAAC3F,KAAK,CAAC;IAEhC;IACA,IAAI,IAAI,CAACtB,YAAY,CAACkH,MAAM,IAAI,IAAI,CAACjH,MAAM,CAACI,eAAe,IAAI,GAAG,CAAC,EAAE;MACnE,IAAI,CAACL,YAAY,GAAG,IAAI,CAACA,YAAY,CAACmH,KAAK,CACzC,CAAC,EACD,IAAI,CAAClH,MAAM,CAACI,eAAe,IAAI,GAAG,CACnC;;IAGH,IAAI,CAACV,aAAa,CAACe,IAAI,CAAC,CAAC,GAAG,IAAI,CAACV,YAAY,CAAC,CAAC;EACjD;EAEA;;;EAGQkG,gBAAgBA,CAAC5E,KAAoB;IAC3C,MAAM8F,YAAY,GAAG,IAAI,CAACxH,WAAW,CAACyH,KAAK;IAE3C;IACAD,YAAY,CAAC3C,WAAW,EAAE;IAE1B;IACA2C,YAAY,CAAC1C,gBAAgB,CAACpD,KAAK,CAACS,QAAQ,CAAC,GAC3C,CAACqF,YAAY,CAAC1C,gBAAgB,CAACpD,KAAK,CAACS,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAE1D;IACAqF,YAAY,CAACzC,gBAAgB,CAACrD,KAAK,CAACW,QAAQ,CAAC,GAC3C,CAACmF,YAAY,CAACzC,gBAAgB,CAACrD,KAAK,CAACW,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAE1D;IACA,IAAIX,KAAK,CAACN,UAAU,EAAE;MACpBoG,YAAY,CAACxC,kBAAkB,CAACtD,KAAK,CAACN,UAAU,CAAC,GAC/C,CAACoG,YAAY,CAACxC,kBAAkB,CAACtD,KAAK,CAACN,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;IAGhE;IACA,IAAIM,KAAK,CAACgB,QAAQ,EAAE;MAClB,MAAMgF,aAAa,GAAGF,YAAY,CAACvC,gBAAgB,CAACb,IAAI,CACrDC,CAAC,IAAKA,CAAC,CAAC3B,QAAQ,KAAKhB,KAAK,CAACgB,QAAQ,CACrC;MACD,IAAIgF,aAAa,EAAE;QACjBA,aAAa,CAACC,KAAK,EAAE;OACtB,MAAM;QACLH,YAAY,CAACvC,gBAAgB,CAAC5B,IAAI,CAAC;UACjCX,QAAQ,EAAEhB,KAAK,CAACgB,QAAQ;UACxBiF,KAAK,EAAE;SACR,CAAC;;MAGJ;MACAH,YAAY,CAACvC,gBAAgB,CAAC2C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACH,KAAK,GAAGE,CAAC,CAACF,KAAK,CAAC;MAC/DH,YAAY,CAACvC,gBAAgB,GAAGuC,YAAY,CAACvC,gBAAgB,CAACsC,KAAK,CACjE,CAAC,EACD,EAAE,CACH;;IAGH;IACA,MAAMQ,UAAU,GAAG,IAAI5C,IAAI,CAACzD,KAAK,CAACH,SAAS,CAAC;IAC5CwG,UAAU,CAACC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE9B,MAAMC,aAAa,GAAGT,YAAY,CAACtC,WAAW,CAACd,IAAI,CAChD8D,CAAC,IAAKA,CAAC,CAAC3G,SAAS,CAACC,OAAO,EAAE,KAAKuG,UAAU,CAACvG,OAAO,EAAE,CACtD;IAED,IAAIyG,aAAa,EAAE;MACjBA,aAAa,CAACN,KAAK,EAAE;KACtB,MAAM;MACLH,YAAY,CAACtC,WAAW,CAAC7B,IAAI,CAAC;QAAE9B,SAAS,EAAEwG,UAAU;QAAEJ,KAAK,EAAE;MAAC,CAAE,CAAC;;IAGpE;IACA,MAAMQ,SAAS,GAAG,IAAIhD,IAAI,CAACA,IAAI,CAACiD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAC5DZ,YAAY,CAACtC,WAAW,GAAGsC,YAAY,CAACtC,WAAW,CAAC9F,MAAM,CACvD8I,CAAC,IAAKA,CAAC,CAAC3G,SAAS,IAAI4G,SAAS,CAChC;IAED,IAAI,CAACnI,WAAW,CAACc,IAAI,CAAC0G,YAAY,CAAC;EACrC;EAEA;;;EAGQhB,QAAQA,CAAC9E,KAAoB;IACnC,MAAMf,QAAQ,GAAG,IAAI,CAACN,MAAM,CAACM,QAAQ,IAAI,OAAO;IAChD,MAAM0H,UAAU,GAAG,IAAI3G,KAAK,CAACW,QAAQ,CAACiG,WAAW,EAAE,KAAK5G,KAAK,CAACS,QAAQ,KACpET,KAAK,CAACP,OACR,EAAE;IACF,MAAMoH,OAAO,GAAG;MACdtG,EAAE,EAAEP,KAAK,CAACO,EAAE;MACZE,QAAQ,EAAET,KAAK,CAACS,QAAQ;MACxBE,QAAQ,EAAEX,KAAK,CAACW,QAAQ;MACxBjB,UAAU,EAAEM,KAAK,CAACN,UAAU;MAC5BsB,QAAQ,EAAEhB,KAAK,CAACgB,QAAQ;MACxBb,OAAO,EAAEH,KAAK,CAACG,OAAO;MACtBgE,aAAa,EAAEnE,KAAK,CAACmE;KACtB;IAED,QAAQlF,QAAQ;MACd,KAAK,OAAO;QACVgG,OAAO,CAACC,KAAK,CAACyB,UAAU,EAAEE,OAAO,CAAC;QAClC;MACF,KAAK,MAAM;QACT,IAAI7G,KAAK,CAACW,QAAQ,KAAK7C,aAAa,CAACgJ,GAAG,EAAE;UACxC7B,OAAO,CAAC8B,IAAI,CAACJ,UAAU,EAAEE,OAAO,CAAC;SAClC,MAAM;UACL5B,OAAO,CAACjF,KAAK,CAAC2G,UAAU,EAAEE,OAAO,CAAC;;QAEpC;MACF,KAAK,MAAM;QACT,IACE7G,KAAK,CAACW,QAAQ,KAAK7C,aAAa,CAACgJ,GAAG,IACpC9G,KAAK,CAACW,QAAQ,KAAK7C,aAAa,CAAC8C,MAAM,EACvC;UACAqE,OAAO,CAAC+B,IAAI,CAACL,UAAU,EAAEE,OAAO,CAAC;SAClC,MAAM;UACL5B,OAAO,CAACjF,KAAK,CAAC2G,UAAU,EAAEE,OAAO,CAAC;;QAEpC;MACF,KAAK,OAAO;MACZ;QACE5B,OAAO,CAACjF,KAAK,CAAC2G,UAAU,EAAEE,OAAO,CAAC;QAClC;;EAEN;EAEA;;;EAGQnD,eAAeA,CAAA;IACrB,OAAO,SAASD,IAAI,CAACiD,GAAG,EAAE,IAAI/G,IAAI,CAACsH,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;EAC7E;EAEA;;;EAGQrD,iBAAiBA,CAAC9D,KAAU;IAClC,MAAMgB,QAAQ,GAAGhB,KAAK,CAACA,KAAK,EAAEgB,QAAQ,EAAEoG,WAAW,EAAE,IAAI,EAAE;IAE3D,IACEpG,QAAQ,CAACe,QAAQ,CAAC,MAAM,CAAC,IACzBf,QAAQ,CAACe,QAAQ,CAAC,OAAO,CAAC,IAC1Bf,QAAQ,CAACe,QAAQ,CAAC,OAAO,CAAC,EAC1B;MACA,OAAOhE,aAAa,CAACsJ,cAAc;;IAErC,IACErG,QAAQ,CAACe,QAAQ,CAAC,YAAY,CAAC,IAC/Bf,QAAQ,CAACe,QAAQ,CAAC,QAAQ,CAAC,IAC3Bf,QAAQ,CAACe,QAAQ,CAAC,WAAW,CAAC,EAC9B;MACA,OAAOhE,aAAa,CAACuJ,aAAa;;IAEpC,IACEtG,QAAQ,CAACe,QAAQ,CAAC,YAAY,CAAC,IAC/Bf,QAAQ,CAACe,QAAQ,CAAC,SAAS,CAAC,IAC5Bf,QAAQ,CAACe,QAAQ,CAAC,UAAU,CAAC,EAC7B;MACA,OAAOhE,aAAa,CAACwJ,UAAU;;IAEjC,IACEvG,QAAQ,CAACe,QAAQ,CAAC,UAAU,CAAC,IAC7Bf,QAAQ,CAACe,QAAQ,CAAC,MAAM,CAAC,IACzBf,QAAQ,CAACe,QAAQ,CAAC,OAAO,CAAC,EAC1B;MACA,OAAOhE,aAAa,CAACyJ,cAAc;;IAErC,IAAIxG,QAAQ,CAACe,QAAQ,CAAC,OAAO,CAAC,IAAIf,QAAQ,CAACe,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC3D,OAAOhE,aAAa,CAAC0J,UAAU;;IAEjC,IACEzG,QAAQ,CAACe,QAAQ,CAAC,UAAU,CAAC,IAC7Bf,QAAQ,CAACe,QAAQ,CAAC,SAAS,CAAC,IAC5Bf,QAAQ,CAACe,QAAQ,CAAC,KAAK,CAAC,EACxB;MACA,OAAOhE,aAAa,CAAC2J,gBAAgB;;IAGvC,OAAO3J,aAAa,CAAC2C,MAAM;EAC7B;EAEA;;;EAGQqD,iBAAiBA,CAAC/D,KAAU;IAClC,MAAMgB,QAAQ,GAAGhB,KAAK,CAACA,KAAK,EAAEgB,QAAQ,EAAEoG,WAAW,EAAE,IAAI,EAAE;IAE3D,IAAIpG,QAAQ,CAACe,QAAQ,CAAC,UAAU,CAAC,IAAIf,QAAQ,CAACe,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC/D,OAAOjE,aAAa,CAAC6J,QAAQ;;IAE/B,IAAI3G,QAAQ,CAACe,QAAQ,CAAC,MAAM,CAAC,IAAIf,QAAQ,CAACe,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC5D,OAAOjE,aAAa,CAACoG,IAAI;;IAE3B,IAAIlD,QAAQ,CAACe,QAAQ,CAAC,KAAK,CAAC,IAAIf,QAAQ,CAACe,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC1D,OAAOjE,aAAa,CAACgJ,GAAG;;IAG1B,OAAOhJ,aAAa,CAAC8C,MAAM;EAC7B;EAEA;;;EAGQoD,mBAAmBA,CAACtE,UAAkB;IAC5C,IAAIA,UAAU,KAAK,GAAG,EAAE;MACtB,OAAO3B,aAAa,CAACsJ,cAAc;;IAErC,IAAI3H,UAAU,KAAK,GAAG,EAAE;MACtB,OAAO3B,aAAa,CAACuJ,aAAa;;IAEpC,IAAI5H,UAAU,IAAI,GAAG,IAAIA,UAAU,GAAG,GAAG,EAAE;MACzC,OAAO3B,aAAa,CAACwJ,UAAU;;IAEjC,IAAI7H,UAAU,IAAI,GAAG,EAAE;MACrB,OAAO3B,aAAa,CAAC2C,MAAM;;IAE7B,IAAIhB,UAAU,KAAK,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;MACtC,OAAO3B,aAAa,CAAC6J,OAAO;;IAG9B,OAAO7J,aAAa,CAAC2C,MAAM;EAC7B;EAEA;;;EAGQuD,2BAA2BA,CAACvE,UAAkB;IACpD,IAAIA,UAAU,IAAI,GAAG,EAAE;MACrB,OAAO5B,aAAa,CAACoG,IAAI;;IAE3B,IAAIxE,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,GAAG,EAAE;MAC5C,OAAO5B,aAAa,CAAC8C,MAAM;;IAE7B,IAAIlB,UAAU,KAAK,GAAG,EAAE;MACtB,OAAO5B,aAAa,CAACgJ,GAAG;;IAE1B,IAAIpH,UAAU,IAAI,GAAG,IAAIA,UAAU,GAAG,GAAG,EAAE;MACzC,OAAO5B,aAAa,CAAC8C,MAAM;;IAE7B,IAAIlB,UAAU,KAAK,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;MACtC,OAAO5B,aAAa,CAACoG,IAAI;;IAG3B,OAAOpG,aAAa,CAAC8C,MAAM;EAC7B;EAEA;;;EAGQ6D,WAAWA,CAACoD,MAAW,EAAEnI,UAAmB;IAClD;IACA,IAAIA,UAAU,KAAK,CAAC,IAAIA,UAAU,KAAKmE,SAAS,EAAE;MAChD,OAAO,IAAI;;IAGb;IACA,IAAInE,UAAU,IAAI,GAAG,EAAE;MACrB,OAAO,IAAI;;IAGb;IACA,IAAIA,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,GAAG,EAAE;MAC5C,OAAO,IAAI;;IAGb;IACA,IAAIA,UAAU,KAAK,GAAG,EAAE;MACtB,OAAO,IAAI;;IAGb;IACA,IAAIA,UAAU,IAAI,GAAG,IAAIA,UAAU,GAAG,GAAG,EAAE;MACzC,OAAO,KAAK;;IAGd,OAAO,KAAK;EACd;EAEA;;;EAGQsF,qBAAqBA,CAAChF,KAAoB;IAChD,OAAO;MACL,GAAGA,KAAK,CAACmE,aAAa;MACtBP,MAAM,EAAE5D,KAAK,CAACN,UAAU;MACxBM,KAAK,EAAE;QACLgB,QAAQ,EAAEhB,KAAK,CAACgB,QAAQ;QACxBvB,OAAO,EAAEO,KAAK,CAACP,OAAO;QACtBqI,YAAY,EAAE9H,KAAK,CAACG,OAAO,CAAC4H;OAC7B;MACDhH,aAAa,EAAEf,KAAK,CAACe;KACtB;EACH;EAAC,QAAAiH,CAAA,G;qBAvrBUhK,YAAY;EAAA;EAAA,QAAAiK,EAAA,G;WAAZjK,YAAY;IAAAkK,OAAA,EAAZlK,YAAY,CAAAmK,IAAA;IAAAC,UAAA,EAFX;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}