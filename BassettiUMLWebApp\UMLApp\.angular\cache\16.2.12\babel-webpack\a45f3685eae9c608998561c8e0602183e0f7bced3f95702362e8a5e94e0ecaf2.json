{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { ErrorMessages } from './error-messages';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/errors/error.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@fortawesome/angular-fontawesome\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@ngx-translate/core\";\nconst _c0 = [\"dialogError\"];\nconst _c1 = function (a1) {\n  return [\"fal\", a1];\n};\nconst _c2 = function (a1, a2) {\n  return {\n    \"error-details-container\": true,\n    \"details-opened\": a1,\n    \"details-closed\": a2\n  };\n};\nfunction DialogMessageComponent_ng_template_0_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵelement(2, \"fa-icon\", 8);\n    i0.ɵɵelementStart(3, \"span\", 9);\n    i0.ɵɵlistener(\"click\", function DialogMessageComponent_ng_template_0_ng_container_9_Template_span_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.showDetails = !ctx_r3.showDetails);\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction1(8, _c1, ctx_r2.showDetails ? \"chevron-down\" : \"chevron-right\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 4, \"errorMessage.details\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(10, _c2, ctx_r2.showDetails, !ctx_r2.showDetails));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 6, ctx_r2.error.details), \" \");\n  }\n}\nconst _c3 = function () {\n  return [\"fal\", \"info-circle\"];\n};\nconst _c4 = function (a0) {\n  return {\n    timeout: a0\n  };\n};\nfunction DialogMessageComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"fa-icon\", 1);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 2)(6, \"div\", 3);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DialogMessageComponent_ng_template_0_ng_container_9_Template, 9, 13, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 5)(11, \"button\", 6);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(\"dialog-header \" + (ctx_r1.error == null ? null : ctx_r1.error.type) + \"-bg\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(16, _c3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 9, ctx_r1.error == null ? null : ctx_r1.error.header));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 11, ctx_r1.error == null ? null : ctx_r1.error.content, i0.ɵɵpureFunction1(17, _c4, ctx_r1.sessionTimeout)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.error == null ? null : ctx_r1.error.details);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap((ctx_r1.error == null ? null : ctx_r1.error.type) + \"-bg\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 14, (ctx_r1.error == null ? null : ctx_r1.error.action) ? ctx_r1.error.action.name : \"window.close\"));\n  }\n}\nexport let DialogMessageComponent = /*#__PURE__*/(() => {\n  class DialogMessageComponent {\n    constructor(errorService, dialog) {\n      this.errorService = errorService;\n      this.dialog = dialog;\n      this.showDetails = false;\n      this.sessionTimeout = 0;\n      this.ngUnsubscribe = new Subject();\n      this.errorMessageDictionary = new ErrorMessages();\n      this.showPopup = false;\n      this.initializeErrors();\n    }\n    ngOnInit() {\n      setTimeout(() => {\n        this.showPopup = true;\n      }, 150);\n    }\n    ngOnDestroy() {\n      this.ngUnsubscribe.next();\n      this.ngUnsubscribe.complete();\n    }\n    /* Private Methods */\n    resetValues() {\n      this.error = {};\n      this.showDetails = false;\n    }\n    initializeErrors() {\n      this.errorService.getErrorsLegacy().pipe(takeUntil(this.ngUnsubscribe)).subscribe(error => {\n        this.resetValues();\n        if (error.isCustomError === true) {\n          this.error = error;\n        } else {\n          if (error.error && error.error.errorKey) {\n            this.error = this.errorMessageDictionary.getMessageByKey(error.error.errorKey);\n            if (!this.error) {\n              this.error = this.errorMessageDictionary.getUnknownError(error); // if the error is not found, we have to add it into the dictionary\n            } else if (error.error.errorDetails) {\n              this.error.details = JSON.stringify(error.error.errorDetails);\n            }\n          } else {\n            const statusCode = error.status || error.statusCode || -1;\n            if (statusCode >= 0) {\n              this.error = this.errorMessageDictionary.getDefaultMessageByCode(statusCode, JSON.stringify(error));\n            } else {\n              this.error = this.errorMessageDictionary.getDefaultMessageByCode(-1, JSON.stringify(error));\n            }\n          }\n        }\n        if (this.dialogError) {\n          this.dialog.open(this.dialogError, {\n            disableClose: true,\n            panelClass: 'customErrorDialog'\n          });\n        }\n      });\n    }\n    static #_ = this.ɵfac = function DialogMessageComponent_Factory(t) {\n      return new (t || DialogMessageComponent)(i0.ɵɵdirectiveInject(i1.ErrorService), i0.ɵɵdirectiveInject(i2.MatDialog));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DialogMessageComponent,\n      selectors: [[\"app-dialog-message\"]],\n      viewQuery: function DialogMessageComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialogError = _t.first);\n        }\n      },\n      decls: 2,\n      vars: 0,\n      consts: [[\"dialogError\", \"\"], [\"size\", \"lg\", 3, \"icon\"], [1, \"dialog-content-container\"], [1, \"user-message\"], [4, \"ngIf\"], [1, \"button-container\"], [\"mat-button\", \"\", \"mat-dialog-close\", \"\"], [1, \"show-detail-container\"], [\"size\", \"lg\", 1, \"accent\", 3, \"icon\"], [1, \"display-detail-span\", \"accent\", 3, \"click\"], [3, \"ngClass\"]],\n      template: function DialogMessageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, DialogMessageComponent_ng_template_0_Template, 14, 19, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgIf, i4.FaIconComponent, i5.MatButton, i2.MatDialogClose, i6.TranslatePipe],\n      styles: [\".dialog-header[_ngcontent-%COMP%]{padding:12px;display:inherit;font-size:20px;color:#fff}.dialog-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{margin:auto;padding-left:8px}.dialog-content-container[_ngcontent-%COMP%]{padding:16px}.dialog-content-container[_ngcontent-%COMP%]   .user-message[_ngcontent-%COMP%]{padding-bottom:8px}.dialog-content-container[_ngcontent-%COMP%]   .show-detail-container[_ngcontent-%COMP%]   fa-icon[_ngcontent-%COMP%]{font-size:9px;margin-right:6px}.dialog-content-container[_ngcontent-%COMP%]   .error-details-container[_ngcontent-%COMP%]{margin-top:16px;transition:max-height .3s linear}.dialog-content-container[_ngcontent-%COMP%]   .details-opened[_ngcontent-%COMP%]{max-height:150px;word-break:break-all;overflow:scroll}.dialog-content-container[_ngcontent-%COMP%]   .details-closed[_ngcontent-%COMP%]{max-height:0;overflow:hidden}.dialog-content-container[_ngcontent-%COMP%]   .display-detail-span[_ngcontent-%COMP%]{text-decoration:underline;cursor:pointer}.button-container[_ngcontent-%COMP%]{padding-bottom:16px;padding-right:16px;display:flex;justify-content:flex-end}.button-container[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{color:#fff!important}\"]\n    });\n  }\n  return DialogMessageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}