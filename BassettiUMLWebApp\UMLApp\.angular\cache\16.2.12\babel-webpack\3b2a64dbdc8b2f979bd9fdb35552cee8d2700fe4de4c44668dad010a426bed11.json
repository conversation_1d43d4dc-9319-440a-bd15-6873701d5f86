{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let AuthParserService = /*#__PURE__*/(() => {\n  class AuthParserService {\n    constructor() {}\n    intercept(request, next) {\n      request = request.clone({\n        withCredentials: true\n        // setHeaders: {\n        //   Authorization: environment.bearerToken,\n        // },\n      });\n      return next.handle(request);\n    }\n    static #_ = this.ɵfac = function AuthParserService_Factory(t) {\n      return new (t || AuthParserService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthParserService,\n      factory: AuthParserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthParserService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}