{"ast": null, "code": "import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';\nexport class ErrorMessages {\n  constructor() {\n    this.defaultMessages = [{\n      errorCode: -1,\n      type: 'error',\n      header: _('errors.unhandled.header'),\n      content: _('errors.unhandled.content'),\n      details: ''\n    }, {\n      errorCode: 0,\n      type: 'error',\n      header: _('errors.0.header'),\n      content: _('errors.0.content'),\n      details: ''\n    }, {\n      errorCode: 400,\n      type: 'error',\n      header: _('errors.400.default.header'),\n      content: _('errors.400.default.content'),\n      details: ''\n    }, {\n      errorCode: 401,\n      type: 'error',\n      header: _('errors.401.default.header'),\n      content: _('errors.401.default.content'),\n      details: ''\n    }, {\n      errorCode: 403,\n      type: 'error',\n      header: _('errors.403.default.header'),\n      content: _('errors.403.default.content'),\n      details: ''\n    }, {\n      errorCode: 404,\n      type: 'error',\n      header: _('errors.404.default.header'),\n      content: _('errors.404.default.content'),\n      details: ''\n    }, {\n      errorCode: 500,\n      type: 'error',\n      header: _('errors.500.default.header'),\n      content: _('errors.500.default.content'),\n      details: ''\n    }];\n    this.keyMessages = [];\n  }\n  getMessageByKey(msgKey) {\n    return this.keyMessages.find(msg => msg.key === msgKey);\n  }\n  getDefaultMessageByCode(errorCode, errorDetails) {\n    const defaultMessage = this.defaultMessages.find(msg => msg.errorCode === errorCode);\n    if (defaultMessage) {\n      defaultMessage.details = errorDetails;\n    }\n    return defaultMessage;\n  }\n  getUnknownError(error) {\n    // Handle both legacy and enhanced error formats\n    const errorKey = error.errorKey || error.error?.errorKey || 'unknown';\n    const statusCode = error.statusCode || error.status;\n    const message = error.message || error.error?.message || '';\n    const unknownError = {\n      key: errorKey,\n      type: 'warn',\n      header: `Error ${statusCode}`,\n      content: message,\n      details: `Add the error key \"${errorKey}\" to handle multilingualism.`\n    };\n    console.warn('[Missing error] - Add this error key to handle multilingualism into the error-messages.ts', unknownError);\n    return unknownError;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}